<?xml version="1.0" encoding="utf-8"?>
<resources>
    <item type="id" name="action0" />
    <item type="id" name="action_bar" />
    <item type="id" name="action_bar_activity_content" />
    <item type="id" name="action_bar_container" />
    <item type="id" name="action_bar_root" />
    <item type="id" name="action_bar_spinner" />
    <item type="id" name="action_bar_subtitle" />
    <item type="id" name="action_bar_title" />
    <item type="id" name="action_container" />
    <item type="id" name="action_context_bar" />
    <item type="id" name="action_divider" />
    <item type="id" name="action_image" />
    <item type="id" name="action_menu_divider" />
    <item type="id" name="action_menu_presenter" />
    <item type="id" name="action_mode_bar" />
    <item type="id" name="action_mode_bar_stub" />
    <item type="id" name="action_mode_close_button" />
    <item type="id" name="action_text" />
    <item type="id" name="actions" />
    <item type="id" name="activity_chooser_view_content" />
    <item type="id" name="alertTitle" />
    <item type="id" name="btnAddProfile" />
    <item type="id" name="btn_delete" />
    <item type="id" name="btn_save" />
    <item type="id" name="buttonPanel" />
    <item type="id" name="buttonTest" />
    <item type="id" name="cancel_action" />
    <item type="id" name="checkbox" />
    <item type="id" name="chronometer" />
    <item type="id" name="container" />
    <item type="id" name="content" />
    <item type="id" name="contentPanel" />
    <item type="id" name="coordinator" />
    <item type="id" name="custom" />
    <item type="id" name="customPanel" />
    <item type="id" name="decor_content_parent" />
    <item type="id" name="default_activity_button" />
    <item type="id" name="design_bottom_sheet" />
    <item type="id" name="design_menu_item_action_area" />
    <item type="id" name="design_menu_item_action_area_stub" />
    <item type="id" name="design_menu_item_text" />
    <item type="id" name="design_navigation_view" />
    <item type="id" name="editText" />
    <item type="id" name="editTextValue" />
    <item type="id" name="editValue" />
    <item type="id" name="edit_drop" />
    <item type="id" name="edit_name" />
    <item type="id" name="edit_pass" />
    <item type="id" name="edit_query" />
    <item type="id" name="end_padder" />
    <item type="id" name="expand_activities_button" />
    <item type="id" name="expanded_menu" />
    <item type="id" name="fastscroller" />
    <item type="id" name="fw_info_root" />
    <item type="id" name="fw_root" />
    <item type="id" name="ghost_view" />
    <item type="id" name="group_divider" />
    <item type="id" name="home" />
    <item type="id" name="icon" />
    <item type="id" name="icon_group" />
    <item type="id" name="image" />
    <item type="id" name="imageViewPkg" />
    <item type="id" name="img_expand" />
    <item type="id" name="img_icon" />
    <item type="id" name="img_start" />
    <item type="id" name="info" />
    <item type="id" name="item_touch_helper_previous_elevation" />
    <item type="id" name="itemicon" />
    <item type="id" name="largeLabel" />
    <item type="id" name="layoutProfileName" />
    <item type="id" name="layout_process" />
    <item type="id" name="lbl_main_ip" />
    <item type="id" name="line1" />
    <item type="id" name="line3" />
    <item type="id" name="list" />
    <item type="id" name="listViewEdit" />
    <item type="id" name="list_item" />
    <item type="id" name="loading" />
    <item type="id" name="logout" />
    <item type="id" name="lv_profileName" />
    <item type="id" name="masked" />
    <item type="id" name="media_actions" />
    <item type="id" name="menu_login_out" />
    <item type="id" name="menu_quit" />
    <item type="id" name="menu_settings" />
    <item type="id" name="message" />
    <item type="id" name="mtrl_child_content_container" />
    <item type="id" name="mtrl_internal_children_alpha_tag" />
    <item type="id" name="navigation_header_container" />
    <item type="id" name="net_config_list" />
    <item type="id" name="net_edit_root" />
    <item type="id" name="meteor_particle_1" />
    <item type="id" name="meteor_particle_2" />
    <item type="id" name="meteor_particle_3" />
    <item type="id" name="meteor_trail_1" />
    <item type="id" name="meteor_trail_2" />
    <item type="id" name="meteor_trail_3" />
    <item type="id" name="small_particle_1" />
    <item type="id" name="small_particle_2" />
    <item type="id" name="small_particle_3" />
    <!-- 满屏星星ID -->
    <item type="id" name="star_1" />
    <item type="id" name="star_2" />
    <item type="id" name="star_3" />
    <item type="id" name="star_4" />
    <item type="id" name="star_5" />
    <item type="id" name="star_6" />
    <item type="id" name="star_7" />
    <item type="id" name="star_8" />
    <item type="id" name="star_9" />
    <item type="id" name="star_10" />
    <item type="id" name="star_11" />
    <item type="id" name="star_12" />
    <item type="id" name="star_13" />
    <item type="id" name="star_14" />
    <item type="id" name="star_15" />
    <item type="id" name="star_16" />
    <item type="id" name="star_17" />
    <item type="id" name="star_18" />
    <item type="id" name="star_19" />
    <item type="id" name="star_20" />
    <item type="id" name="star_21" />
    <item type="id" name="star_22" />
    <item type="id" name="star_23" />
    <item type="id" name="star_24" />
    <item type="id" name="notification_background" />
    <item type="id" name="notification_main_column" />
    <item type="id" name="notification_main_column_container" />
    <item type="id" name="oalogin" />
    <item type="id" name="parentPanel" />
    <item type="id" name="parent_matrix" />
    <item type="id" name="pb_checkVersion" />
    <item type="id" name="progress_circular" />
    <item type="id" name="progress_horizontal" />
    <item type="id" name="qqlogin" />
    <item type="id" name="radio" />
    <item type="id" name="radiogroup_protocol" />
    <item type="id" name="right_icon" />
    <item type="id" name="right_side" />
    <item type="id" name="rootActivityLayout" />
    <item type="id" name="save_image_matrix" />
    <item type="id" name="save_non_transition_alpha" />
    <item type="id" name="save_scale_type" />
    <item type="id" name="scrollIndicatorDown" />
    <item type="id" name="scrollIndicatorUp" />
    <item type="id" name="scrollView" />
    <item type="id" name="search_badge" />
    <item type="id" name="search_bar" />
    <item type="id" name="search_button" />
    <item type="id" name="search_close_btn" />
    <item type="id" name="search_edit_frame" />
    <item type="id" name="search_go_btn" />
    <item type="id" name="search_mag_icon" />
    <item type="id" name="search_plate" />
    <item type="id" name="search_src_text" />
    <item type="id" name="search_voice_btn" />
    <item type="id" name="select_dialog_listview" />
    <item type="id" name="select_indicator" />
    <item type="id" name="shortcut" />
    <item type="id" name="smallLabel" />
    <item type="id" name="snackbar_action" />
    <item type="id" name="snackbar_text" />
    <item type="id" name="spacer" />
    <item type="id" name="split_action_bar" />
    <item type="id" name="status_bar_latest_event_content" />
    <item type="id" name="submenuarrow" />
    <item type="id" name="submit_area" />
    <item type="id" name="tableRowContent" />
    <item type="id" name="tag_transition_group" />
    <item type="id" name="tag_unhandled_key_event_manager" />
    <item type="id" name="tag_unhandled_key_listeners" />
    <item type="id" name="template_desc" />
    <item type="id" name="template_edit" />
    <item type="id" name="template_name" />
    <item type="id" name="text" />
    <item type="id" name="text2" />
    <item type="id" name="textSpacerNoButtons" />
    <item type="id" name="textSpacerNoTitle" />
    <item type="id" name="textView1" />
    <item type="id" name="textView2" />
    <item type="id" name="textViewID" />
    <item type="id" name="textViewName" />
    <item type="id" name="textViewPkg" />
    <item type="id" name="text_input_password_toggle" />
    <item type="id" name="textinput_counter" />
    <item type="id" name="textinput_error" />
    <item type="id" name="textinput_helper_text" />
    <item type="id" name="time" />
    <item type="id" name="title" />
    <item type="id" name="titleDividerNoCustom" />
    <item type="id" name="title_template" />
    <item type="id" name="toolbar" />
    <item type="id" name="toolbar_title" />
    <item type="id" name="topPanel" />
    <item type="id" name="touch_outside" />
    <item type="id" name="transition_current_scene" />
    <item type="id" name="transition_layout_save" />
    <item type="id" name="transition_position" />
    <item type="id" name="transition_scene_layoutid_cache" />
    <item type="id" name="transition_transform" />
    <item type="id" name="tv_checkVersion" />
    <item type="id" name="txtProfileName" />
    <item type="id" name="txtVersion" />
    <item type="id" name="txt_active" />
    <item type="id" name="txt_info" />
    <item type="id" name="txt_item" />
    <item type="id" name="txt_ping" />
    <item type="id" name="txt_profile" />
    <item type="id" name="up" />
    <item type="id" name="user_pic" />
    <item type="id" name="view_offset_helper" />
    <item type="id" name="visible" />
    <item type="id" name="webview" />
    <item type="id" name="drag_indicator" />
    <item type="id" name="selection_indicator" />
    <item type="id" name="status_icon" />
    <item type="id" name="fw_main_container" />
    <item type="id" name="status_indicator" />
    <item type="id" name="network_quality_indicator" />
</resources>
