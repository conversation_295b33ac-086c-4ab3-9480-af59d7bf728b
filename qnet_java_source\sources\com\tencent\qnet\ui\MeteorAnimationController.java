package com.tencent.qnet.ui;

import android.app.Activity;
import android.util.Log;
import android.view.View;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.ImageView;
import com.tencent.qnet.rick.R;

/**
 * 流星粒子动画控制器
 * 控制动态背景中的流星粒子效果
 */
public class MeteorAnimationController {
    
    private Activity activity;
    private boolean isAnimating = false;
    
    // 流星粒子视图
    private ImageView meteorParticle1;
    private ImageView meteorParticle2;
    private ImageView meteorParticle3;
    private ImageView meteorTrail1;
    private ImageView meteorTrail2;
    private ImageView meteorTrail3;
    private ImageView smallParticle1;
    private ImageView smallParticle2;
    private ImageView smallParticle3;
    
    // 动画对象
    private Animation meteorAnim1;
    private Animation meteorAnim2;
    private Animation meteorAnim3;
    
    public MeteorAnimationController(Activity activity) {
        this.activity = activity;
        initViews();
        initAnimations();
    }
    
    /**
     * 初始化视图
     */
    private void initViews() {
        Log.d("MeteorAnimationController", "Initializing views...");

        meteorParticle1 = activity.findViewById(R.id.meteor_particle_1);
        meteorParticle2 = activity.findViewById(R.id.meteor_particle_2);
        meteorParticle3 = activity.findViewById(R.id.meteor_particle_3);
        meteorTrail1 = activity.findViewById(R.id.meteor_trail_1);
        meteorTrail2 = activity.findViewById(R.id.meteor_trail_2);
        meteorTrail3 = activity.findViewById(R.id.meteor_trail_3);
        smallParticle1 = activity.findViewById(R.id.small_particle_1);
        smallParticle2 = activity.findViewById(R.id.small_particle_2);
        smallParticle3 = activity.findViewById(R.id.small_particle_3);

        // 调试信息
        Log.d("MeteorAnimationController", "meteorParticle1: " + (meteorParticle1 != null ? "found" : "null"));
        Log.d("MeteorAnimationController", "meteorParticle2: " + (meteorParticle2 != null ? "found" : "null"));
        Log.d("MeteorAnimationController", "meteorParticle3: " + (meteorParticle3 != null ? "found" : "null"));
        Log.d("MeteorAnimationController", "meteorTrail1: " + (meteorTrail1 != null ? "found" : "null"));
        Log.d("MeteorAnimationController", "meteorTrail2: " + (meteorTrail2 != null ? "found" : "null"));
        Log.d("MeteorAnimationController", "meteorTrail3: " + (meteorTrail3 != null ? "found" : "null"));
    }
    
    /**
     * 初始化动画
     */
    private void initAnimations() {
        Log.d("MeteorAnimationController", "Loading animations...");
        try {
            meteorAnim1 = AnimationUtils.loadAnimation(activity, R.anim.meteor_particle_1);
            meteorAnim2 = AnimationUtils.loadAnimation(activity, R.anim.meteor_particle_2);
            meteorAnim3 = AnimationUtils.loadAnimation(activity, R.anim.meteor_particle_3);

            Log.d("MeteorAnimationController", "meteorAnim1: " + (meteorAnim1 != null ? "loaded" : "null"));
            Log.d("MeteorAnimationController", "meteorAnim2: " + (meteorAnim2 != null ? "loaded" : "null"));
            Log.d("MeteorAnimationController", "meteorAnim3: " + (meteorAnim3 != null ? "loaded" : "null"));
        } catch (Exception e) {
            Log.e("MeteorAnimationController", "Failed to load animations", e);
        }
    }
    
    /**
     * 开始流星动画
     */
    public void startMeteorAnimation() {
        if (isAnimating) return;
        
        isAnimating = true;
        
        // 启动主要流星动画
        if (meteorParticle1 != null && meteorAnim1 != null) {
            meteorParticle1.startAnimation(meteorAnim1);
            if (meteorTrail1 != null) {
                meteorTrail1.startAnimation(meteorAnim1);
            }
        }
        
        if (meteorParticle2 != null && meteorAnim2 != null) {
            meteorParticle2.startAnimation(meteorAnim2);
            if (meteorTrail2 != null) {
                meteorTrail2.startAnimation(meteorAnim2);
            }
        }
        
        if (meteorParticle3 != null && meteorAnim3 != null) {
            meteorParticle3.startAnimation(meteorAnim3);
            if (meteorTrail3 != null) {
                meteorTrail3.startAnimation(meteorAnim3);
            }
        }
        
        // 启动小粒子动画
        startSmallParticleAnimations();
    }
    
    /**
     * 启动小粒子动画
     */
    private void startSmallParticleAnimations() {
        // 小粒子1 - 慢速闪烁
        if (smallParticle1 != null) {
            Animation twinkle1 = AnimationUtils.loadAnimation(activity, R.anim.purple_pulse_animation);
            twinkle1.setStartOffset(500);
            smallParticle1.startAnimation(twinkle1);
        }
        
        // 小粒子2 - 中速闪烁
        if (smallParticle2 != null) {
            Animation twinkle2 = AnimationUtils.loadAnimation(activity, R.anim.purple_pulse_animation);
            twinkle2.setStartOffset(1500);
            smallParticle2.startAnimation(twinkle2);
        }
        
        // 小粒子3 - 快速闪烁
        if (smallParticle3 != null) {
            Animation twinkle3 = AnimationUtils.loadAnimation(activity, R.anim.purple_pulse_animation);
            twinkle3.setStartOffset(2500);
            smallParticle3.startAnimation(twinkle3);
        }
    }
    
    /**
     * 停止流星动画
     */
    public void stopMeteorAnimation() {
        if (!isAnimating) return;
        
        isAnimating = false;
        
        // 停止所有动画
        if (meteorParticle1 != null) meteorParticle1.clearAnimation();
        if (meteorParticle2 != null) meteorParticle2.clearAnimation();
        if (meteorParticle3 != null) meteorParticle3.clearAnimation();
        if (meteorTrail1 != null) meteorTrail1.clearAnimation();
        if (meteorTrail2 != null) meteorTrail2.clearAnimation();
        if (meteorTrail3 != null) meteorTrail3.clearAnimation();
        if (smallParticle1 != null) smallParticle1.clearAnimation();
        if (smallParticle2 != null) smallParticle2.clearAnimation();
        if (smallParticle3 != null) smallParticle3.clearAnimation();
    }
    
    /**
     * 检查动画是否正在运行
     */
    public boolean isAnimating() {
        return isAnimating;
    }
    
    /**
     * 重启动画
     */
    public void restartAnimation() {
        stopMeteorAnimation();
        // 延迟重启以确保动画完全停止
        if (activity != null) {
            activity.runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    try {
                        Thread.sleep(100);
                        startMeteorAnimation();
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                }
            });
        }
    }
}
