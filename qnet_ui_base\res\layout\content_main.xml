<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    app:layout_behavior="@string/appbar_scrolling_view_behavior"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <!-- 底部按钮区域 - 距离屏幕底部8px -->
    <LinearLayout
        android:id="@+id/bottom_button_area"
        android:layout_width="match_parent"
        android:layout_height="80dp"
        android:layout_alignParentBottom="true"
        android:layout_marginBottom="60dp"
        android:orientation="horizontal"
        android:paddingLeft="16dp"
        android:paddingRight="16dp"
        android:paddingTop="12dp"
        android:paddingBottom="4dp"
        android:background="@android:color/transparent"
        android:elevation="8dp"
        android:gravity="center">

        <Button
            android:id="@id/buttonTest"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:text="@string/button_start_test"
            android:textSize="16sp"
            android:textColor="@android:color/white"
            android:textStyle="bold"
            android:background="@drawable/enhanced_button_bg"
            android:fontFamily="sans-serif-medium"
            android:elevation="4dp"
            android:stateListAnimator="@animator/button_state_animator" />

    </LinearLayout>

    <!-- 可滚动内容区域 - 在按钮上方 -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_above="@id/bottom_button_area"
        android:layout_marginBottom="4dp"
        android:fillViewport="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- 应用信息区域 - 紧凑设计 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:background="@color/colorPrimary"
                android:paddingTop="12dp"
                android:paddingBottom="12dp"
                android:orientation="vertical"
                android:minHeight="100dp">

                <LinearLayout
                    android:id="@id/layout_process"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center"
                    android:layout_gravity="center">

                    <!-- 应用图标 -->
                    <ImageView
                        android:id="@id/imageViewPkg"
                        android:layout_width="56dp"
                        android:layout_height="56dp"
                        android:background="@drawable/minimal_card_bg"
                        android:padding="10dp"
                        android:scaleType="centerInside"
                        app:srcCompat="@android:drawable/sym_def_app_icon"
                        android:layout_marginEnd="10dp" />

                    <!-- 应用名称 -->
                    <TextView
                        android:id="@id/textViewPkg"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/lable_select_progress"
                        android:textSize="16sp"
                        android:textColor="@android:color/white"
                        android:textStyle="bold"
                        android:fontFamily="sans-serif-medium"
                        android:gravity="center_vertical" />

                </LinearLayout>

            </LinearLayout>

            <!-- 配置内容区域 -->
            <FrameLayout
                android:id="@id/tableRowContent"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/minimal_card_bg"
                android:layout_marginLeft="8dp"
                android:layout_marginRight="8dp"
                android:layout_marginTop="4dp"
                android:layout_marginBottom="30dp">

                <include
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    layout="@layout/layout_content" />

            </FrameLayout>

        </LinearLayout>

    </ScrollView>

</RelativeLayout>