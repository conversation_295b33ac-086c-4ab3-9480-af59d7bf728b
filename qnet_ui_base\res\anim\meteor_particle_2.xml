<?xml version="1.0" encoding="utf-8"?>
<set xmlns:android="http://schemas.android.com/apk/res/android"
    android:repeatCount="infinite"
    android:repeatMode="restart">
    
    <!-- 流星轨迹移动 - 反向 -->
    <translate
        android:fromXDelta="100%"
        android:toXDelta="-100%"
        android:fromYDelta="0%"
        android:toYDelta="100%"
        android:duration="2500"
        android:startOffset="500"
        android:interpolator="@android:anim/linear_interpolator" />
        
    <!-- 流星闪烁效果 -->
    <alpha
        android:fromAlpha="0.0"
        android:toAlpha="0.8"
        android:duration="400"
        android:startOffset="1000"
        android:interpolator="@android:anim/accelerate_interpolator" />
        
    <alpha
        android:fromAlpha="0.8"
        android:toAlpha="0.2"
        android:duration="3200"
        android:startOffset="1400"
        android:interpolator="@android:anim/decelerate_interpolator" />
        
    <alpha
        android:fromAlpha="0.2"
        android:toAlpha="0.0"
        android:duration="400"
        android:startOffset="4600"
        android:interpolator="@android:anim/accelerate_interpolator" />
        
    <!-- 流星缩放效果 -->
    <scale
        android:fromXScale="0.3"
        android:toXScale="1.2"
        android:fromYScale="0.3"
        android:toYScale="1.2"
        android:pivotX="50%"
        android:pivotY="50%"
        android:duration="400"
        android:startOffset="1000"
        android:interpolator="@android:anim/overshoot_interpolator" />
        
    <scale
        android:fromXScale="1.2"
        android:toXScale="0.6"
        android:fromYScale="1.2"
        android:toYScale="0.6"
        android:pivotX="50%"
        android:pivotY="50%"
        android:duration="3600"
        android:startOffset="1400"
        android:interpolator="@android:anim/decelerate_interpolator" />
        
</set>
