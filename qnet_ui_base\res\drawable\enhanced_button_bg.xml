<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- 按下状态 -->
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <gradient
                android:startColor="#7a80a8"
                android:endColor="#6b7194"
                android:angle="90" />
            <corners android:radius="28dp" />
            <stroke
                android:width="2dp"
                android:color="#6b7194" />
        </shape>
    </item>
    
    <!-- 禁用状态 -->
    <item android:state_enabled="false">
        <shape android:shape="rectangle">
            <solid android:color="#c4c7db" />
            <corners android:radius="28dp" />
        </shape>
    </item>

    <!-- 正常状态 -->
    <item>
        <shape android:shape="rectangle">
            <gradient
                android:startColor="#979dc3"
                android:endColor="#7a80a8"
                android:angle="90" />
            <corners android:radius="28dp" />
            <stroke
                android:width="1dp"
                android:color="#7a80a8" />
        </shape>
    </item>
    
</selector>
