<?xml version="1.0" encoding="utf-8"?>
<android.support.design.widget.CoordinatorLayout
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    android:background="@drawable/meteor_background_gradient"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <!-- 满屏粒子星空效果 -->
    <!-- 第一行粒子 -->
    <ImageView android:id="@+id/star_1" android:layout_width="12dp" android:layout_height="12dp" android:src="@drawable/simple_star" android:layout_gravity="start|top" android:layout_marginLeft="30dp" android:layout_marginTop="80dp" />
    <ImageView android:id="@+id/star_2" android:layout_width="8dp" android:layout_height="8dp" android:src="@drawable/simple_star" android:layout_gravity="start|top" android:layout_marginLeft="120dp" android:layout_marginTop="60dp" />
    <ImageView android:id="@+id/star_3" android:layout_width="14dp" android:layout_height="14dp" android:src="@drawable/simple_star" android:layout_gravity="start|top" android:layout_marginLeft="220dp" android:layout_marginTop="90dp" />
    <ImageView android:id="@+id/star_4" android:layout_width="10dp" android:layout_height="10dp" android:src="@drawable/simple_star" android:layout_gravity="start|top" android:layout_marginLeft="320dp" android:layout_marginTop="70dp" />

    <!-- 第二行粒子 -->
    <ImageView android:id="@+id/star_5" android:layout_width="16dp" android:layout_height="16dp" android:src="@drawable/simple_star" android:layout_gravity="start|top" android:layout_marginLeft="60dp" android:layout_marginTop="180dp" />
    <ImageView android:id="@+id/star_6" android:layout_width="6dp" android:layout_height="6dp" android:src="@drawable/simple_star" android:layout_gravity="start|top" android:layout_marginLeft="150dp" android:layout_marginTop="160dp" />
    <ImageView android:id="@+id/star_7" android:layout_width="12dp" android:layout_height="12dp" android:src="@drawable/simple_star" android:layout_gravity="start|top" android:layout_marginLeft="250dp" android:layout_marginTop="190dp" />
    <ImageView android:id="@+id/star_8" android:layout_width="18dp" android:layout_height="18dp" android:src="@drawable/simple_star" android:layout_gravity="start|top" android:layout_marginLeft="340dp" android:layout_marginTop="170dp" />

    <!-- 第三行粒子 -->
    <ImageView android:id="@+id/star_9" android:layout_width="10dp" android:layout_height="10dp" android:src="@drawable/simple_star" android:layout_gravity="start|top" android:layout_marginLeft="40dp" android:layout_marginTop="280dp" />
    <ImageView android:id="@+id/star_10" android:layout_width="14dp" android:layout_height="14dp" android:src="@drawable/simple_star" android:layout_gravity="start|top" android:layout_marginLeft="130dp" android:layout_marginTop="260dp" />
    <ImageView android:id="@+id/star_11" android:layout_width="8dp" android:layout_height="8dp" android:src="@drawable/simple_star" android:layout_gravity="start|top" android:layout_marginLeft="230dp" android:layout_marginTop="290dp" />
    <ImageView android:id="@+id/star_12" android:layout_width="16dp" android:layout_height="16dp" android:src="@drawable/simple_star" android:layout_gravity="start|top" android:layout_marginLeft="320dp" android:layout_marginTop="270dp" />

    <!-- 第四行粒子 -->
    <ImageView android:id="@+id/star_13" android:layout_width="12dp" android:layout_height="12dp" android:src="@drawable/simple_star" android:layout_gravity="start|top" android:layout_marginLeft="70dp" android:layout_marginTop="380dp" />
    <ImageView android:id="@+id/star_14" android:layout_width="20dp" android:layout_height="20dp" android:src="@drawable/simple_star" android:layout_gravity="start|top" android:layout_marginLeft="160dp" android:layout_marginTop="360dp" />
    <ImageView android:id="@+id/star_15" android:layout_width="6dp" android:layout_height="6dp" android:src="@drawable/simple_star" android:layout_gravity="start|top" android:layout_marginLeft="260dp" android:layout_marginTop="390dp" />
    <ImageView android:id="@+id/star_16" android:layout_width="14dp" android:layout_height="14dp" android:src="@drawable/simple_star" android:layout_gravity="start|top" android:layout_marginLeft="350dp" android:layout_marginTop="370dp" />

    <!-- 第五行粒子 -->
    <ImageView android:id="@+id/star_17" android:layout_width="18dp" android:layout_height="18dp" android:src="@drawable/simple_star" android:layout_gravity="start|top" android:layout_marginLeft="50dp" android:layout_marginTop="480dp" />
    <ImageView android:id="@+id/star_18" android:layout_width="8dp" android:layout_height="8dp" android:src="@drawable/simple_star" android:layout_gravity="start|top" android:layout_marginLeft="140dp" android:layout_marginTop="460dp" />
    <ImageView android:id="@+id/star_19" android:layout_width="16dp" android:layout_height="16dp" android:src="@drawable/simple_star" android:layout_gravity="start|top" android:layout_marginLeft="240dp" android:layout_marginTop="490dp" />
    <ImageView android:id="@+id/star_20" android:layout_width="10dp" android:layout_height="10dp" android:src="@drawable/simple_star" android:layout_gravity="start|top" android:layout_marginLeft="330dp" android:layout_marginTop="470dp" />

    <!-- 第六行粒子 -->
    <ImageView android:id="@+id/star_21" android:layout_width="14dp" android:layout_height="14dp" android:src="@drawable/simple_star" android:layout_gravity="start|top" android:layout_marginLeft="80dp" android:layout_marginTop="580dp" />
    <ImageView android:id="@+id/star_22" android:layout_width="12dp" android:layout_height="12dp" android:src="@drawable/simple_star" android:layout_gravity="start|top" android:layout_marginLeft="170dp" android:layout_marginTop="560dp" />
    <ImageView android:id="@+id/star_23" android:layout_width="6dp" android:layout_height="6dp" android:src="@drawable/simple_star" android:layout_gravity="start|top" android:layout_marginLeft="270dp" android:layout_marginTop="590dp" />
    <ImageView android:id="@+id/star_24" android:layout_width="18dp" android:layout_height="18dp" android:src="@drawable/simple_star" android:layout_gravity="start|top" android:layout_marginLeft="360dp" android:layout_marginTop="570dp" />

    <!-- 流星粒子效果 -->
    <ImageView
        android:id="@+id/meteor_particle_1"
        android:layout_width="12dp"
        android:layout_height="12dp"
        android:src="@drawable/meteor_particle"
        android:layout_gravity="start|top"
        android:layout_marginLeft="10dp"
        android:layout_marginTop="100dp"
        android:alpha="1.0" />

    <ImageView
        android:id="@+id/meteor_particle_2"
        android:layout_width="10dp"
        android:layout_height="10dp"
        android:src="@drawable/meteor_particle"
        android:layout_gravity="end|top"
        android:layout_marginRight="20dp"
        android:layout_marginTop="150dp"
        android:alpha="1.0" />

    <ImageView
        android:id="@+id/meteor_particle_3"
        android:layout_width="14dp"
        android:layout_height="14dp"
        android:src="@drawable/meteor_particle"
        android:layout_gravity="center_horizontal|top"
        android:layout_marginTop="200dp"
        android:alpha="1.0" />

    <!-- 流星尾迹效果 -->
    <ImageView
        android:id="@+id/meteor_trail_1"
        android:layout_width="30dp"
        android:layout_height="3dp"
        android:src="@drawable/meteor_trail"
        android:layout_gravity="start|top"
        android:layout_marginLeft="10dp"
        android:layout_marginTop="100dp"
        android:rotation="45"
        android:alpha="0.8" />

    <ImageView
        android:id="@+id/meteor_trail_2"
        android:layout_width="25dp"
        android:layout_height="3dp"
        android:src="@drawable/meteor_trail"
        android:layout_gravity="end|top"
        android:layout_marginRight="20dp"
        android:layout_marginTop="150dp"
        android:rotation="-30"
        android:alpha="0.8" />

    <ImageView
        android:id="@+id/meteor_trail_3"
        android:layout_width="35dp"
        android:layout_height="3dp"
        android:src="@drawable/meteor_trail"
        android:layout_gravity="center_horizontal|top"
        android:layout_marginTop="200dp"
        android:rotation="15"
        android:alpha="0.8" />

    <!-- 小粒子效果 -->
    <ImageView
        android:id="@+id/small_particle_1"
        android:layout_width="4dp"
        android:layout_height="4dp"
        android:src="@drawable/meteor_particle"
        android:layout_gravity="start|center_vertical"
        android:layout_marginLeft="80dp"
        android:alpha="0.0" />

    <ImageView
        android:id="@+id/small_particle_2"
        android:layout_width="3dp"
        android:layout_height="3dp"
        android:src="@drawable/meteor_particle"
        android:layout_gravity="end|center_vertical"
        android:layout_marginRight="100dp"
        android:alpha="0.0" />

    <ImageView
        android:id="@+id/small_particle_3"
        android:layout_width="5dp"
        android:layout_height="5dp"
        android:src="@drawable/meteor_particle"
        android:layout_gravity="center"
        android:alpha="0.0" />

    <!-- 现代化AppBar -->
    <android.support.design.widget.AppBarLayout
        android:theme="@style/AppTheme.AppBarOverlay"
        android:background="@color/colorPrimary"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:elevation="8dp"
        android:animateLayoutChanges="true">

        <android.support.v7.widget.Toolbar
            android:id="@id/toolbar"
            android:background="@color/colorPrimary"
            android:layout_width="match_parent"
            android:layout_height="?actionBarSize"
            app:popupTheme="@style/AppTheme.PopupOverlay"
            app:layout_scrollFlags="scroll|enterAlways">

            <!-- 标题带图标 -->
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <ImageView
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:src="@drawable/custom_image_circle"
                    android:layout_marginEnd="8dp"
                    android:scaleType="centerCrop" />

                <TextView
                    android:id="@id/toolbar_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/action_title"
                    android:textSize="20sp"
                    android:textStyle="bold"
                    android:textColor="@android:color/white"
                    android:fontFamily="sans-serif-medium" />

            </LinearLayout>

        </android.support.v7.widget.Toolbar>

    </android.support.design.widget.AppBarLayout>

    <include layout="@layout/content_main" />

</android.support.design.widget.CoordinatorLayout>