<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:orientation="vertical"
    android:background="@color/colorNetBackground"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="8dp"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <!-- 配置列表标题 -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="网络配置"
        android:textSize="16sp"
        android:textColor="#89000000"
        android:fontFamily="sans-serif-medium"
        android:padding="8dp"
        android:layout_marginBottom="4dp" />

    <!-- 配置列表 - 限制高度 -->
    <ListView
        android:id="@id/net_config_list"
        android:background="@drawable/config_list_bg"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:divider="@drawable/list_divider"
        android:dividerHeight="1dp"
        android:scrollbars="vertical"
        android:fadeScrollbars="true"
        android:clipToPadding="false"
        android:paddingTop="4dp"
        android:paddingBottom="4dp"
        android:maxHeight="250dp" />

    <!-- 添加配置按钮 -->
    <LinearLayout
        android:id="@id/btnAddProfile"
        android:background="@drawable/add_profile_button_bg"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        android:layout_marginBottom="8dp"
        android:elevation="2dp">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/btn_add_profile"
            android:textSize="16sp"
            android:textColor="@android:color/white"
            android:fontFamily="sans-serif-medium"
            android:background="?android:attr/selectableItemBackground"
            android:padding="16dp"
            android:gravity="center"
            android:drawableLeft="@drawable/ic_add"
            android:drawablePadding="8dp" />

    </LinearLayout>

</LinearLayout>