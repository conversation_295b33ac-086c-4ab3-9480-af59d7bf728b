<?xml version="1.0" encoding="utf-8"?>
<vector android:height="32.0dip" android:width="32.0dip" android:viewportWidth="24.0" android:viewportHeight="24.0"
  xmlns:android="http://schemas.android.com/apk/res/android">
  
    <!-- 过渡状态2 - 背景继续变亮 -->
    <path android:fillColor="#979dc3"
          android:pathData="M12,2C6.48,2 2,6.48 2,12s4.48,10 10,10s10,-4.48 10,-10S17.52,2 12,2z" />
    
    <!-- 暂停条进一步收缩，开始形成三角形 -->
    <path android:fillColor="#FFFFFF" 
          android:pathData="M9,8h2v8h-2V8zM13,9v6l3,-3z" />
          
</vector>
