<?xml version="1.0" encoding="utf-8"?>
<resources>
    <public type="anim" name="abc_fade_in" id="0x7f010000" />
    <public type="anim" name="abc_fade_out" id="0x7f010001" />
    <public type="anim" name="abc_grow_fade_in_from_bottom" id="0x7f010002" />
    <public type="anim" name="abc_popup_enter" id="0x7f010003" />
    <public type="anim" name="abc_popup_exit" id="0x7f010004" />
    <public type="anim" name="abc_shrink_fade_out_from_bottom" id="0x7f010005" />
    <public type="anim" name="abc_slide_in_bottom" id="0x7f010006" />
    <public type="anim" name="abc_slide_in_top" id="0x7f010007" />
    <public type="anim" name="abc_slide_out_bottom" id="0x7f010008" />
    <public type="anim" name="abc_slide_out_top" id="0x7f010009" />
    <public type="anim" name="abc_tooltip_enter" id="0x7f01000a" />
    <public type="anim" name="abc_tooltip_exit" id="0x7f01000b" />
    <public type="anim" name="design_bottom_sheet_slide_in" id="0x7f01000c" />
    <public type="anim" name="design_bottom_sheet_slide_out" id="0x7f01000d" />
    <public type="anim" name="design_snackbar_in" id="0x7f01000e" />
    <public type="anim" name="design_snackbar_out" id="0x7f01000f" />
    <public type="anim" name="icon_scale_bounce" id="0x7f010010" />
    <public type="anim" name="icon_rotate_fade" id="0x7f010011" />
    <public type="anim" name="icon_pulse" id="0x7f010012" />
    <public type="anim" name="rotate_continuous" id="0x7f010013" />
    <public type="anim" name="fade_in" id="0x7f010014" />
    <public type="anim" name="fade_out" id="0x7f010015" />
    <public type="anim" name="slide_in_right" id="0x7f010016" />
    <public type="anim" name="slide_out_left" id="0x7f010017" />
    <public type="anim" name="purple_pulse_animation" id="0x7f010018" />
    <public type="anim" name="meteor_particle_1" id="0x7f010019" />
    <public type="anim" name="meteor_particle_2" id="0x7f01001a" />
    <public type="anim" name="meteor_particle_3" id="0x7f01001b" />
    <public type="anim" name="simple_move" id="0x7f01001c" />
    <public type="anim" name="simple_blink" id="0x7f01001d" />
    <public type="animator" name="design_appbar_state_list_animator" id="0x7f020000" />
    <public type="animator" name="button_state_animator" id="0x7f020020" />
    <public type="animator" name="design_fab_hide_motion_spec" id="0x7f020001" />
    <public type="animator" name="design_fab_show_motion_spec" id="0x7f020002" />
    <public type="animator" name="fastscroll__default_hide" id="0x7f020003" />
    <public type="animator" name="fastscroll__default_show" id="0x7f020004" />
    <public type="animator" name="mtrl_btn_state_list_anim" id="0x7f020005" />
    <public type="animator" name="mtrl_btn_unelevated_state_list_anim" id="0x7f020006" />
    <public type="animator" name="mtrl_chip_state_list_anim" id="0x7f020007" />
    <public type="animator" name="mtrl_fab_hide_motion_spec" id="0x7f020008" />
    <public type="animator" name="mtrl_fab_show_motion_spec" id="0x7f020009" />
    <public type="animator" name="mtrl_fab_transformation_sheet_collapse_spec" id="0x7f02000a" />
    <public type="animator" name="mtrl_fab_transformation_sheet_expand_spec" id="0x7f02000b" />
    <public type="array" name="arr_protocols" id="0x7f030000" />
    <public type="attr" name="actionBarDivider" id="0x7f040000" />
    <public type="attr" name="actionBarItemBackground" id="0x7f040001" />
    <public type="attr" name="actionBarPopupTheme" id="0x7f040002" />
    <public type="attr" name="actionBarSize" id="0x7f040003" />
    <public type="attr" name="actionBarSplitStyle" id="0x7f040004" />
    <public type="attr" name="actionBarStyle" id="0x7f040005" />
    <public type="attr" name="actionBarTabBarStyle" id="0x7f040006" />
    <public type="attr" name="actionBarTabStyle" id="0x7f040007" />
    <public type="attr" name="actionBarTabTextStyle" id="0x7f040008" />
    <public type="attr" name="actionBarTheme" id="0x7f040009" />
    <public type="attr" name="actionBarWidgetTheme" id="0x7f04000a" />
    <public type="attr" name="actionButtonStyle" id="0x7f04000b" />
    <public type="attr" name="actionDropDownStyle" id="0x7f04000c" />
    <public type="attr" name="actionLayout" id="0x7f04000d" />
    <public type="attr" name="actionMenuTextAppearance" id="0x7f04000e" />
    <public type="attr" name="actionMenuTextColor" id="0x7f04000f" />
    <public type="attr" name="actionModeBackground" id="0x7f040010" />
    <public type="attr" name="actionModeCloseButtonStyle" id="0x7f040011" />
    <public type="attr" name="actionModeCloseDrawable" id="0x7f040012" />
    <public type="attr" name="actionModeCopyDrawable" id="0x7f040013" />
    <public type="attr" name="actionModeCutDrawable" id="0x7f040014" />
    <public type="attr" name="actionModeFindDrawable" id="0x7f040015" />
    <public type="attr" name="actionModePasteDrawable" id="0x7f040016" />
    <public type="attr" name="actionModePopupWindowStyle" id="0x7f040017" />
    <public type="attr" name="actionModeSelectAllDrawable" id="0x7f040018" />
    <public type="attr" name="actionModeShareDrawable" id="0x7f040019" />
    <public type="attr" name="actionModeSplitBackground" id="0x7f04001a" />
    <public type="attr" name="actionModeStyle" id="0x7f04001b" />
    <public type="attr" name="actionModeWebSearchDrawable" id="0x7f04001c" />
    <public type="attr" name="actionOverflowButtonStyle" id="0x7f04001d" />
    <public type="attr" name="actionOverflowMenuStyle" id="0x7f04001e" />
    <public type="attr" name="actionProviderClass" id="0x7f04001f" />
    <public type="attr" name="actionViewClass" id="0x7f040020" />
    <public type="attr" name="activityChooserViewStyle" id="0x7f040021" />
    <public type="attr" name="alertDialogButtonGroupStyle" id="0x7f040022" />
    <public type="attr" name="alertDialogCenterButtons" id="0x7f040023" />
    <public type="attr" name="alertDialogStyle" id="0x7f040024" />
    <public type="attr" name="alertDialogTheme" id="0x7f040025" />
    <public type="attr" name="allowStacking" id="0x7f040026" />
    <public type="attr" name="alpha" id="0x7f040027" />
    <public type="attr" name="alphabeticModifiers" id="0x7f040028" />
    <public type="attr" name="arrowHeadLength" id="0x7f040029" />
    <public type="attr" name="arrowShaftLength" id="0x7f04002a" />
    <public type="attr" name="autoCompleteTextViewStyle" id="0x7f04002b" />
    <public type="attr" name="autoSizeMaxTextSize" id="0x7f04002c" />
    <public type="attr" name="autoSizeMinTextSize" id="0x7f04002d" />
    <public type="attr" name="autoSizePresetSizes" id="0x7f04002e" />
    <public type="attr" name="autoSizeStepGranularity" id="0x7f04002f" />
    <public type="attr" name="autoSizeTextType" id="0x7f040030" />
    <public type="attr" name="background" id="0x7f040031" />
    <public type="attr" name="backgroundSplit" id="0x7f040032" />
    <public type="attr" name="backgroundStacked" id="0x7f040033" />
    <public type="attr" name="backgroundTint" id="0x7f040034" />
    <public type="attr" name="backgroundTintMode" id="0x7f040035" />
    <public type="attr" name="barLength" id="0x7f040036" />
    <public type="attr" name="behavior_autoHide" id="0x7f040037" />
    <public type="attr" name="behavior_fitToContents" id="0x7f040038" />
    <public type="attr" name="behavior_hideable" id="0x7f040039" />
    <public type="attr" name="behavior_overlapTop" id="0x7f04003a" />
    <public type="attr" name="behavior_peekHeight" id="0x7f04003b" />
    <public type="attr" name="behavior_skipCollapsed" id="0x7f04003c" />
    <public type="attr" name="borderWidth" id="0x7f04003d" />
    <public type="attr" name="borderlessButtonStyle" id="0x7f04003e" />
    <public type="attr" name="bottomAppBarStyle" id="0x7f04003f" />
    <public type="attr" name="bottomNavigationStyle" id="0x7f040040" />
    <public type="attr" name="bottomSheetDialogTheme" id="0x7f040041" />
    <public type="attr" name="bottomSheetStyle" id="0x7f040042" />
    <public type="attr" name="boxBackgroundColor" id="0x7f040043" />
    <public type="attr" name="boxBackgroundMode" id="0x7f040044" />
    <public type="attr" name="boxCollapsedPaddingTop" id="0x7f040045" />
    <public type="attr" name="boxCornerRadiusBottomEnd" id="0x7f040046" />
    <public type="attr" name="boxCornerRadiusBottomStart" id="0x7f040047" />
    <public type="attr" name="boxCornerRadiusTopEnd" id="0x7f040048" />
    <public type="attr" name="boxCornerRadiusTopStart" id="0x7f040049" />
    <public type="attr" name="boxStrokeColor" id="0x7f04004a" />
    <public type="attr" name="boxStrokeWidth" id="0x7f04004b" />
    <public type="attr" name="buttonBarButtonStyle" id="0x7f04004c" />
    <public type="attr" name="buttonBarNegativeButtonStyle" id="0x7f04004d" />
    <public type="attr" name="buttonBarNeutralButtonStyle" id="0x7f04004e" />
    <public type="attr" name="buttonBarPositiveButtonStyle" id="0x7f04004f" />
    <public type="attr" name="buttonBarStyle" id="0x7f040050" />
    <public type="attr" name="buttonGravity" id="0x7f040051" />
    <public type="attr" name="buttonIconDimen" id="0x7f040052" />
    <public type="attr" name="buttonPanelSideLayout" id="0x7f040053" />
    <public type="attr" name="buttonStyle" id="0x7f040054" />
    <public type="attr" name="buttonStyleSmall" id="0x7f040055" />
    <public type="attr" name="buttonTint" id="0x7f040056" />
    <public type="attr" name="buttonTintMode" id="0x7f040057" />
    <public type="attr" name="cardBackgroundColor" id="0x7f040058" />
    <public type="attr" name="cardCornerRadius" id="0x7f040059" />
    <public type="attr" name="cardElevation" id="0x7f04005a" />
    <public type="attr" name="cardMaxElevation" id="0x7f04005b" />
    <public type="attr" name="cardPreventCornerOverlap" id="0x7f04005c" />
    <public type="attr" name="cardUseCompatPadding" id="0x7f04005d" />
    <public type="attr" name="cardViewStyle" id="0x7f04005e" />
    <public type="attr" name="checkboxStyle" id="0x7f04005f" />
    <public type="attr" name="checkedChip" id="0x7f040060" />
    <public type="attr" name="checkedIcon" id="0x7f040061" />
    <public type="attr" name="checkedIconEnabled" id="0x7f040062" />
    <public type="attr" name="checkedIconVisible" id="0x7f040063" />
    <public type="attr" name="checkedTextViewStyle" id="0x7f040064" />
    <public type="attr" name="chipBackgroundColor" id="0x7f040065" />
    <public type="attr" name="chipCornerRadius" id="0x7f040066" />
    <public type="attr" name="chipEndPadding" id="0x7f040067" />
    <public type="attr" name="chipGroupStyle" id="0x7f040068" />
    <public type="attr" name="chipIcon" id="0x7f040069" />
    <public type="attr" name="chipIconEnabled" id="0x7f04006a" />
    <public type="attr" name="chipIconSize" id="0x7f04006b" />
    <public type="attr" name="chipIconTint" id="0x7f04006c" />
    <public type="attr" name="chipIconVisible" id="0x7f04006d" />
    <public type="attr" name="chipMinHeight" id="0x7f04006e" />
    <public type="attr" name="chipSpacing" id="0x7f04006f" />
    <public type="attr" name="chipSpacingHorizontal" id="0x7f040070" />
    <public type="attr" name="chipSpacingVertical" id="0x7f040071" />
    <public type="attr" name="chipStandaloneStyle" id="0x7f040072" />
    <public type="attr" name="chipStartPadding" id="0x7f040073" />
    <public type="attr" name="chipStrokeColor" id="0x7f040074" />
    <public type="attr" name="chipStrokeWidth" id="0x7f040075" />
    <public type="attr" name="chipStyle" id="0x7f040076" />
    <public type="attr" name="closeIcon" id="0x7f040077" />
    <public type="attr" name="closeIconEnabled" id="0x7f040078" />
    <public type="attr" name="closeIconEndPadding" id="0x7f040079" />
    <public type="attr" name="closeIconSize" id="0x7f04007a" />
    <public type="attr" name="closeIconStartPadding" id="0x7f04007b" />
    <public type="attr" name="closeIconTint" id="0x7f04007c" />
    <public type="attr" name="closeIconVisible" id="0x7f04007d" />
    <public type="attr" name="closeItemLayout" id="0x7f04007e" />
    <public type="attr" name="collapseContentDescription" id="0x7f04007f" />
    <public type="attr" name="collapseIcon" id="0x7f040080" />
    <public type="attr" name="collapsedTitleGravity" id="0x7f040081" />
    <public type="attr" name="collapsedTitleTextAppearance" id="0x7f040082" />
    <public type="attr" name="color" id="0x7f040083" />
    <public type="attr" name="colorAccent" id="0x7f040084" />
    <public type="attr" name="colorBackgroundFloating" id="0x7f040085" />
    <public type="attr" name="colorButtonNormal" id="0x7f040086" />
    <public type="attr" name="colorControlActivated" id="0x7f040087" />
    <public type="attr" name="colorControlHighlight" id="0x7f040088" />
    <public type="attr" name="colorControlNormal" id="0x7f040089" />
    <public type="attr" name="colorError" id="0x7f04008a" />
    <public type="attr" name="colorPrimary" id="0x7f04008b" />
    <public type="attr" name="colorPrimaryDark" id="0x7f04008c" />
    <public type="attr" name="colorSecondary" id="0x7f04008d" />
    <public type="attr" name="colorSwitchThumbNormal" id="0x7f04008e" />
    <public type="attr" name="commitIcon" id="0x7f04008f" />
    <public type="attr" name="constraintSet" id="0x7f040090" />
    <public type="attr" name="contentDescription" id="0x7f040091" />
    <public type="attr" name="contentInsetEnd" id="0x7f040092" />
    <public type="attr" name="contentInsetEndWithActions" id="0x7f040093" />
    <public type="attr" name="contentInsetLeft" id="0x7f040094" />
    <public type="attr" name="contentInsetRight" id="0x7f040095" />
    <public type="attr" name="contentInsetStart" id="0x7f040096" />
    <public type="attr" name="contentInsetStartWithNavigation" id="0x7f040097" />
    <public type="attr" name="contentPadding" id="0x7f040098" />
    <public type="attr" name="contentPaddingBottom" id="0x7f040099" />
    <public type="attr" name="contentPaddingLeft" id="0x7f04009a" />
    <public type="attr" name="contentPaddingRight" id="0x7f04009b" />
    <public type="attr" name="contentPaddingTop" id="0x7f04009c" />
    <public type="attr" name="contentScrim" id="0x7f04009d" />
    <public type="attr" name="controlBackground" id="0x7f04009e" />
    <public type="attr" name="coordinatorLayoutStyle" id="0x7f04009f" />
    <public type="attr" name="cornerRadius" id="0x7f0400a0" />
    <public type="attr" name="counterEnabled" id="0x7f0400a1" />
    <public type="attr" name="counterMaxLength" id="0x7f0400a2" />
    <public type="attr" name="counterOverflowTextAppearance" id="0x7f0400a3" />
    <public type="attr" name="counterTextAppearance" id="0x7f0400a4" />
    <public type="attr" name="customNavigationLayout" id="0x7f0400a5" />
    <public type="attr" name="defaultQueryHint" id="0x7f0400a6" />
    <public type="attr" name="dialogCornerRadius" id="0x7f0400a7" />
    <public type="attr" name="dialogPreferredPadding" id="0x7f0400a8" />
    <public type="attr" name="dialogTheme" id="0x7f0400a9" />
    <public type="attr" name="displayOptions" id="0x7f0400aa" />
    <public type="attr" name="divider" id="0x7f0400ab" />
    <public type="attr" name="dividerHorizontal" id="0x7f0400ac" />
    <public type="attr" name="dividerPadding" id="0x7f0400ad" />
    <public type="attr" name="dividerVertical" id="0x7f0400ae" />
    <public type="attr" name="drawableSize" id="0x7f0400af" />
    <public type="attr" name="drawerArrowStyle" id="0x7f0400b0" />
    <public type="attr" name="dropDownListViewStyle" id="0x7f0400b1" />
    <public type="attr" name="dropdownListPreferredItemHeight" id="0x7f0400b2" />
    <public type="attr" name="editTextBackground" id="0x7f0400b3" />
    <public type="attr" name="editTextColor" id="0x7f0400b4" />
    <public type="attr" name="editTextStyle" id="0x7f0400b5" />
    <public type="attr" name="elevation" id="0x7f0400b6" />
    <public type="attr" name="enforceMaterialTheme" id="0x7f0400b7" />
    <public type="attr" name="enforceTextAppearance" id="0x7f0400b8" />
    <public type="attr" name="errorEnabled" id="0x7f0400b9" />
    <public type="attr" name="errorTextAppearance" id="0x7f0400ba" />
    <public type="attr" name="expandActivityOverflowButtonDrawable" id="0x7f0400bb" />
    <public type="attr" name="expanded" id="0x7f0400bc" />
    <public type="attr" name="expandedTitleGravity" id="0x7f0400bd" />
    <public type="attr" name="expandedTitleMargin" id="0x7f0400be" />
    <public type="attr" name="expandedTitleMarginBottom" id="0x7f0400bf" />
    <public type="attr" name="expandedTitleMarginEnd" id="0x7f0400c0" />
    <public type="attr" name="expandedTitleMarginStart" id="0x7f0400c1" />
    <public type="attr" name="expandedTitleMarginTop" id="0x7f0400c2" />
    <public type="attr" name="expandedTitleTextAppearance" id="0x7f0400c3" />
    <public type="attr" name="fabAlignmentMode" id="0x7f0400c4" />
    <public type="attr" name="fabCradleMargin" id="0x7f0400c5" />
    <public type="attr" name="fabCradleRoundedCornerRadius" id="0x7f0400c6" />
    <public type="attr" name="fabCradleVerticalOffset" id="0x7f0400c7" />
    <public type="attr" name="fabCustomSize" id="0x7f0400c8" />
    <public type="attr" name="fabSize" id="0x7f0400c9" />
    <public type="attr" name="fastScrollEnabled" id="0x7f0400ca" />
    <public type="attr" name="fastScrollHorizontalThumbDrawable" id="0x7f0400cb" />
    <public type="attr" name="fastScrollHorizontalTrackDrawable" id="0x7f0400cc" />
    <public type="attr" name="fastScrollVerticalThumbDrawable" id="0x7f0400cd" />
    <public type="attr" name="fastScrollVerticalTrackDrawable" id="0x7f0400ce" />
    <public type="attr" name="fastscroll__bubbleColor" id="0x7f0400cf" />
    <public type="attr" name="fastscroll__bubbleTextAppearance" id="0x7f0400d0" />
    <public type="attr" name="fastscroll__handleColor" id="0x7f0400d1" />
    <public type="attr" name="fastscroll__style" id="0x7f0400d2" />
    <public type="attr" name="firstBaselineToTopHeight" id="0x7f0400d3" />
    <public type="attr" name="floatingActionButtonStyle" id="0x7f0400d4" />
    <public type="attr" name="font" id="0x7f0400d5" />
    <public type="attr" name="fontFamily" id="0x7f0400d6" />
    <public type="attr" name="fontProviderAuthority" id="0x7f0400d7" />
    <public type="attr" name="fontProviderCerts" id="0x7f0400d8" />
    <public type="attr" name="fontProviderFetchStrategy" id="0x7f0400d9" />
    <public type="attr" name="fontProviderFetchTimeout" id="0x7f0400da" />
    <public type="attr" name="fontProviderPackage" id="0x7f0400db" />
    <public type="attr" name="fontProviderQuery" id="0x7f0400dc" />
    <public type="attr" name="fontStyle" id="0x7f0400dd" />
    <public type="attr" name="fontVariationSettings" id="0x7f0400de" />
    <public type="attr" name="fontWeight" id="0x7f0400df" />
    <public type="attr" name="foregroundInsidePadding" id="0x7f0400e0" />
    <public type="attr" name="gapBetweenBars" id="0x7f0400e1" />
    <public type="attr" name="goIcon" id="0x7f0400e2" />
    <public type="attr" name="headerLayout" id="0x7f0400e3" />
    <public type="attr" name="height" id="0x7f0400e4" />
    <public type="attr" name="helperText" id="0x7f0400e5" />
    <public type="attr" name="helperTextEnabled" id="0x7f0400e6" />
    <public type="attr" name="helperTextTextAppearance" id="0x7f0400e7" />
    <public type="attr" name="hideMotionSpec" id="0x7f0400e8" />
    <public type="attr" name="hideOnContentScroll" id="0x7f0400e9" />
    <public type="attr" name="hideOnScroll" id="0x7f0400ea" />
    <public type="attr" name="hintAnimationEnabled" id="0x7f0400eb" />
    <public type="attr" name="hintEnabled" id="0x7f0400ec" />
    <public type="attr" name="hintTextAppearance" id="0x7f0400ed" />
    <public type="attr" name="homeAsUpIndicator" id="0x7f0400ee" />
    <public type="attr" name="homeLayout" id="0x7f0400ef" />
    <public type="attr" name="hoveredFocusedTranslationZ" id="0x7f0400f0" />
    <public type="attr" name="icon" id="0x7f0400f1" />
    <public type="attr" name="iconEndPadding" id="0x7f0400f2" />
    <public type="attr" name="iconGravity" id="0x7f0400f3" />
    <public type="attr" name="iconPadding" id="0x7f0400f4" />
    <public type="attr" name="iconSize" id="0x7f0400f5" />
    <public type="attr" name="iconStartPadding" id="0x7f0400f6" />
    <public type="attr" name="iconTint" id="0x7f0400f7" />
    <public type="attr" name="iconTintMode" id="0x7f0400f8" />
    <public type="attr" name="iconifiedByDefault" id="0x7f0400f9" />
    <public type="attr" name="imageButtonStyle" id="0x7f0400fa" />
    <public type="attr" name="indeterminateProgressStyle" id="0x7f0400fb" />
    <public type="attr" name="initialActivityCount" id="0x7f0400fc" />
    <public type="attr" name="insetForeground" id="0x7f0400fd" />
    <public type="attr" name="isLightTheme" id="0x7f0400fe" />
    <public type="attr" name="itemBackground" id="0x7f0400ff" />
    <public type="attr" name="itemHorizontalPadding" id="0x7f040100" />
    <public type="attr" name="itemHorizontalTranslationEnabled" id="0x7f040101" />
    <public type="attr" name="itemIconPadding" id="0x7f040102" />
    <public type="attr" name="itemIconSize" id="0x7f040103" />
    <public type="attr" name="itemIconTint" id="0x7f040104" />
    <public type="attr" name="itemPadding" id="0x7f040105" />
    <public type="attr" name="itemSpacing" id="0x7f040106" />
    <public type="attr" name="itemTextAppearance" id="0x7f040107" />
    <public type="attr" name="itemTextAppearanceActive" id="0x7f040108" />
    <public type="attr" name="itemTextAppearanceInactive" id="0x7f040109" />
    <public type="attr" name="itemTextColor" id="0x7f04010a" />
    <public type="attr" name="keylines" id="0x7f04010b" />
    <public type="attr" name="labelVisibilityMode" id="0x7f04010c" />
    <public type="attr" name="lastBaselineToBottomHeight" id="0x7f04010d" />
    <public type="attr" name="layout" id="0x7f04010e" />
    <public type="attr" name="layoutManager" id="0x7f04010f" />
    <public type="attr" name="layout_anchor" id="0x7f040110" />
    <public type="attr" name="layout_anchorGravity" id="0x7f040111" />
    <public type="attr" name="layout_behavior" id="0x7f040112" />
    <public type="attr" name="layout_collapseMode" id="0x7f040113" />
    <public type="attr" name="layout_collapseParallaxMultiplier" id="0x7f040114" />
    <public type="attr" name="layout_constraintBaseline_creator" id="0x7f040115" />
    <public type="attr" name="layout_constraintBaseline_toBaselineOf" id="0x7f040116" />
    <public type="attr" name="layout_constraintBottom_creator" id="0x7f040117" />
    <public type="attr" name="layout_constraintBottom_toBottomOf" id="0x7f040118" />
    <public type="attr" name="layout_constraintBottom_toTopOf" id="0x7f040119" />
    <public type="attr" name="layout_constraintDimensionRatio" id="0x7f04011a" />
    <public type="attr" name="layout_constraintEnd_toEndOf" id="0x7f04011b" />
    <public type="attr" name="layout_constraintEnd_toStartOf" id="0x7f04011c" />
    <public type="attr" name="layout_constraintGuide_begin" id="0x7f04011d" />
    <public type="attr" name="layout_constraintGuide_end" id="0x7f04011e" />
    <public type="attr" name="layout_constraintGuide_percent" id="0x7f04011f" />
    <public type="attr" name="layout_constraintHeight_default" id="0x7f040120" />
    <public type="attr" name="layout_constraintHeight_max" id="0x7f040121" />
    <public type="attr" name="layout_constraintHeight_min" id="0x7f040122" />
    <public type="attr" name="layout_constraintHorizontal_bias" id="0x7f040123" />
    <public type="attr" name="layout_constraintHorizontal_chainStyle" id="0x7f040124" />
    <public type="attr" name="layout_constraintHorizontal_weight" id="0x7f040125" />
    <public type="attr" name="layout_constraintLeft_creator" id="0x7f040126" />
    <public type="attr" name="layout_constraintLeft_toLeftOf" id="0x7f040127" />
    <public type="attr" name="layout_constraintLeft_toRightOf" id="0x7f040128" />
    <public type="attr" name="layout_constraintRight_creator" id="0x7f040129" />
    <public type="attr" name="layout_constraintRight_toLeftOf" id="0x7f04012a" />
    <public type="attr" name="layout_constraintRight_toRightOf" id="0x7f04012b" />
    <public type="attr" name="layout_constraintStart_toEndOf" id="0x7f04012c" />
    <public type="attr" name="layout_constraintStart_toStartOf" id="0x7f04012d" />
    <public type="attr" name="layout_constraintTop_creator" id="0x7f04012e" />
    <public type="attr" name="layout_constraintTop_toBottomOf" id="0x7f04012f" />
    <public type="attr" name="layout_constraintTop_toTopOf" id="0x7f040130" />
    <public type="attr" name="layout_constraintVertical_bias" id="0x7f040131" />
    <public type="attr" name="layout_constraintVertical_chainStyle" id="0x7f040132" />
    <public type="attr" name="layout_constraintVertical_weight" id="0x7f040133" />
    <public type="attr" name="layout_constraintWidth_default" id="0x7f040134" />
    <public type="attr" name="layout_constraintWidth_max" id="0x7f040135" />
    <public type="attr" name="layout_constraintWidth_min" id="0x7f040136" />
    <public type="attr" name="layout_dodgeInsetEdges" id="0x7f040137" />
    <public type="attr" name="layout_editor_absoluteX" id="0x7f040138" />
    <public type="attr" name="layout_editor_absoluteY" id="0x7f040139" />
    <public type="attr" name="layout_goneMarginBottom" id="0x7f04013a" />
    <public type="attr" name="layout_goneMarginEnd" id="0x7f04013b" />
    <public type="attr" name="layout_goneMarginLeft" id="0x7f04013c" />
    <public type="attr" name="layout_goneMarginRight" id="0x7f04013d" />
    <public type="attr" name="layout_goneMarginStart" id="0x7f04013e" />
    <public type="attr" name="layout_goneMarginTop" id="0x7f04013f" />
    <public type="attr" name="layout_insetEdge" id="0x7f040140" />
    <public type="attr" name="layout_keyline" id="0x7f040141" />
    <public type="attr" name="layout_optimizationLevel" id="0x7f040142" />
    <public type="attr" name="layout_scrollFlags" id="0x7f040143" />
    <public type="attr" name="layout_scrollInterpolator" id="0x7f040144" />
    <public type="attr" name="liftOnScroll" id="0x7f040145" />
    <public type="attr" name="lineHeight" id="0x7f040146" />
    <public type="attr" name="lineSpacing" id="0x7f040147" />
    <public type="attr" name="listChoiceBackgroundIndicator" id="0x7f040148" />
    <public type="attr" name="listDividerAlertDialog" id="0x7f040149" />
    <public type="attr" name="listItemLayout" id="0x7f04014a" />
    <public type="attr" name="listLayout" id="0x7f04014b" />
    <public type="attr" name="listMenuViewStyle" id="0x7f04014c" />
    <public type="attr" name="listPopupWindowStyle" id="0x7f04014d" />
    <public type="attr" name="listPreferredItemHeight" id="0x7f04014e" />
    <public type="attr" name="listPreferredItemHeightLarge" id="0x7f04014f" />
    <public type="attr" name="listPreferredItemHeightSmall" id="0x7f040150" />
    <public type="attr" name="listPreferredItemPaddingLeft" id="0x7f040151" />
    <public type="attr" name="listPreferredItemPaddingRight" id="0x7f040152" />
    <public type="attr" name="logo" id="0x7f040153" />
    <public type="attr" name="logoDescription" id="0x7f040154" />
    <public type="attr" name="materialButtonStyle" id="0x7f040155" />
    <public type="attr" name="materialCardViewStyle" id="0x7f040156" />
    <public type="attr" name="maxActionInlineWidth" id="0x7f040157" />
    <public type="attr" name="maxButtonHeight" id="0x7f040158" />
    <public type="attr" name="maxImageSize" id="0x7f040159" />
    <public type="attr" name="measureWithLargestChild" id="0x7f04015a" />
    <public type="attr" name="menu" id="0x7f04015b" />
    <public type="attr" name="multiChoiceItemLayout" id="0x7f04015c" />
    <public type="attr" name="navigationContentDescription" id="0x7f04015d" />
    <public type="attr" name="navigationIcon" id="0x7f04015e" />
    <public type="attr" name="navigationMode" id="0x7f04015f" />
    <public type="attr" name="navigationViewStyle" id="0x7f040160" />
    <public type="attr" name="numericModifiers" id="0x7f040161" />
    <public type="attr" name="overlapAnchor" id="0x7f040162" />
    <public type="attr" name="paddingBottomNoButtons" id="0x7f040163" />
    <public type="attr" name="paddingEnd" id="0x7f040164" />
    <public type="attr" name="paddingStart" id="0x7f040165" />
    <public type="attr" name="paddingTopNoTitle" id="0x7f040166" />
    <public type="attr" name="panelBackground" id="0x7f040167" />
    <public type="attr" name="panelMenuListTheme" id="0x7f040168" />
    <public type="attr" name="panelMenuListWidth" id="0x7f040169" />
    <public type="attr" name="passwordToggleContentDescription" id="0x7f04016a" />
    <public type="attr" name="passwordToggleDrawable" id="0x7f04016b" />
    <public type="attr" name="passwordToggleEnabled" id="0x7f04016c" />
    <public type="attr" name="passwordToggleTint" id="0x7f04016d" />
    <public type="attr" name="passwordToggleTintMode" id="0x7f04016e" />
    <public type="attr" name="popupMenuStyle" id="0x7f04016f" />
    <public type="attr" name="popupTheme" id="0x7f040170" />
    <public type="attr" name="popupWindowStyle" id="0x7f040171" />
    <public type="attr" name="preserveIconSpacing" id="0x7f040172" />
    <public type="attr" name="pressedTranslationZ" id="0x7f040173" />
    <public type="attr" name="progressBarPadding" id="0x7f040174" />
    <public type="attr" name="progressBarStyle" id="0x7f040175" />
    <public type="attr" name="queryBackground" id="0x7f040176" />
    <public type="attr" name="queryHint" id="0x7f040177" />
    <public type="attr" name="radioButtonStyle" id="0x7f040178" />
    <public type="attr" name="ratingBarStyle" id="0x7f040179" />
    <public type="attr" name="ratingBarStyleIndicator" id="0x7f04017a" />
    <public type="attr" name="ratingBarStyleSmall" id="0x7f04017b" />
    <public type="attr" name="reverseLayout" id="0x7f04017c" />
    <public type="attr" name="rippleColor" id="0x7f04017d" />
    <public type="attr" name="scrimAnimationDuration" id="0x7f04017e" />
    <public type="attr" name="scrimBackground" id="0x7f04017f" />
    <public type="attr" name="scrimVisibleHeightTrigger" id="0x7f040180" />
    <public type="attr" name="searchHintIcon" id="0x7f040181" />
    <public type="attr" name="searchIcon" id="0x7f040182" />
    <public type="attr" name="searchViewStyle" id="0x7f040183" />
    <public type="attr" name="seekBarStyle" id="0x7f040184" />
    <public type="attr" name="selectableItemBackground" id="0x7f040185" />
    <public type="attr" name="selectableItemBackgroundBorderless" id="0x7f040186" />
    <public type="attr" name="showAsAction" id="0x7f040187" />
    <public type="attr" name="showDividers" id="0x7f040188" />
    <public type="attr" name="showMotionSpec" id="0x7f040189" />
    <public type="attr" name="showText" id="0x7f04018a" />
    <public type="attr" name="showTitle" id="0x7f04018b" />
    <public type="attr" name="singleChoiceItemLayout" id="0x7f04018c" />
    <public type="attr" name="singleLine" id="0x7f04018d" />
    <public type="attr" name="singleSelection" id="0x7f04018e" />
    <public type="attr" name="snackbarButtonStyle" id="0x7f04018f" />
    <public type="attr" name="snackbarStyle" id="0x7f040190" />
    <public type="attr" name="spanCount" id="0x7f040191" />
    <public type="attr" name="spinBars" id="0x7f040192" />
    <public type="attr" name="spinnerDropDownItemStyle" id="0x7f040193" />
    <public type="attr" name="spinnerStyle" id="0x7f040194" />
    <public type="attr" name="splitTrack" id="0x7f040195" />
    <public type="attr" name="srcCompat" id="0x7f040196" />
    <public type="attr" name="stackFromEnd" id="0x7f040197" />
    <public type="attr" name="state_above_anchor" id="0x7f040198" />
    <public type="attr" name="state_collapsed" id="0x7f040199" />
    <public type="attr" name="state_collapsible" id="0x7f04019a" />
    <public type="attr" name="state_liftable" id="0x7f04019b" />
    <public type="attr" name="state_lifted" id="0x7f04019c" />
    <public type="attr" name="statusBarBackground" id="0x7f04019d" />
    <public type="attr" name="statusBarScrim" id="0x7f04019e" />
    <public type="attr" name="strokeColor" id="0x7f04019f" />
    <public type="attr" name="strokeWidth" id="0x7f0401a0" />
    <public type="attr" name="subMenuArrow" id="0x7f0401a1" />
    <public type="attr" name="submitBackground" id="0x7f0401a2" />
    <public type="attr" name="subtitle" id="0x7f0401a3" />
    <public type="attr" name="subtitleTextAppearance" id="0x7f0401a4" />
    <public type="attr" name="subtitleTextColor" id="0x7f0401a5" />
    <public type="attr" name="subtitleTextStyle" id="0x7f0401a6" />
    <public type="attr" name="suggestionRowLayout" id="0x7f0401a7" />
    <public type="attr" name="switchMinWidth" id="0x7f0401a8" />
    <public type="attr" name="switchPadding" id="0x7f0401a9" />
    <public type="attr" name="switchStyle" id="0x7f0401aa" />
    <public type="attr" name="switchTextAppearance" id="0x7f0401ab" />
    <public type="attr" name="tabBackground" id="0x7f0401ac" />
    <public type="attr" name="tabContentStart" id="0x7f0401ad" />
    <public type="attr" name="tabGravity" id="0x7f0401ae" />
    <public type="attr" name="tabIconTint" id="0x7f0401af" />
    <public type="attr" name="tabIconTintMode" id="0x7f0401b0" />
    <public type="attr" name="tabIndicator" id="0x7f0401b1" />
    <public type="attr" name="tabIndicatorAnimationDuration" id="0x7f0401b2" />
    <public type="attr" name="tabIndicatorColor" id="0x7f0401b3" />
    <public type="attr" name="tabIndicatorFullWidth" id="0x7f0401b4" />
    <public type="attr" name="tabIndicatorGravity" id="0x7f0401b5" />
    <public type="attr" name="tabIndicatorHeight" id="0x7f0401b6" />
    <public type="attr" name="tabInlineLabel" id="0x7f0401b7" />
    <public type="attr" name="tabMaxWidth" id="0x7f0401b8" />
    <public type="attr" name="tabMinWidth" id="0x7f0401b9" />
    <public type="attr" name="tabMode" id="0x7f0401ba" />
    <public type="attr" name="tabPadding" id="0x7f0401bb" />
    <public type="attr" name="tabPaddingBottom" id="0x7f0401bc" />
    <public type="attr" name="tabPaddingEnd" id="0x7f0401bd" />
    <public type="attr" name="tabPaddingStart" id="0x7f0401be" />
    <public type="attr" name="tabPaddingTop" id="0x7f0401bf" />
    <public type="attr" name="tabRippleColor" id="0x7f0401c0" />
    <public type="attr" name="tabSelectedTextColor" id="0x7f0401c1" />
    <public type="attr" name="tabStyle" id="0x7f0401c2" />
    <public type="attr" name="tabTextAppearance" id="0x7f0401c3" />
    <public type="attr" name="tabTextColor" id="0x7f0401c4" />
    <public type="attr" name="tabUnboundedRipple" id="0x7f0401c5" />
    <public type="attr" name="textAllCaps" id="0x7f0401c6" />
    <public type="attr" name="textAppearanceBody1" id="0x7f0401c7" />
    <public type="attr" name="textAppearanceBody2" id="0x7f0401c8" />
    <public type="attr" name="textAppearanceButton" id="0x7f0401c9" />
    <public type="attr" name="textAppearanceCaption" id="0x7f0401ca" />
    <public type="attr" name="textAppearanceHeadline1" id="0x7f0401cb" />
    <public type="attr" name="textAppearanceHeadline2" id="0x7f0401cc" />
    <public type="attr" name="textAppearanceHeadline3" id="0x7f0401cd" />
    <public type="attr" name="textAppearanceHeadline4" id="0x7f0401ce" />
    <public type="attr" name="textAppearanceHeadline5" id="0x7f0401cf" />
    <public type="attr" name="textAppearanceHeadline6" id="0x7f0401d0" />
    <public type="attr" name="textAppearanceLargePopupMenu" id="0x7f0401d1" />
    <public type="attr" name="textAppearanceListItem" id="0x7f0401d2" />
    <public type="attr" name="textAppearanceListItemSecondary" id="0x7f0401d3" />
    <public type="attr" name="textAppearanceListItemSmall" id="0x7f0401d4" />
    <public type="attr" name="textAppearanceOverline" id="0x7f0401d5" />
    <public type="attr" name="textAppearancePopupMenuHeader" id="0x7f0401d6" />
    <public type="attr" name="textAppearanceSearchResultSubtitle" id="0x7f0401d7" />
    <public type="attr" name="textAppearanceSearchResultTitle" id="0x7f0401d8" />
    <public type="attr" name="textAppearanceSmallPopupMenu" id="0x7f0401d9" />
    <public type="attr" name="textAppearanceSubtitle1" id="0x7f0401da" />
    <public type="attr" name="textAppearanceSubtitle2" id="0x7f0401db" />
    <public type="attr" name="textColorAlertDialogListItem" id="0x7f0401dc" />
    <public type="attr" name="textColorSearchUrl" id="0x7f0401dd" />
    <public type="attr" name="textEndPadding" id="0x7f0401de" />
    <public type="attr" name="textInputStyle" id="0x7f0401df" />
    <public type="attr" name="textStartPadding" id="0x7f0401e0" />
    <public type="attr" name="theme" id="0x7f0401e1" />
    <public type="attr" name="thickness" id="0x7f0401e2" />
    <public type="attr" name="thumbTextPadding" id="0x7f0401e3" />
    <public type="attr" name="thumbTint" id="0x7f0401e4" />
    <public type="attr" name="thumbTintMode" id="0x7f0401e5" />
    <public type="attr" name="tickMark" id="0x7f0401e6" />
    <public type="attr" name="tickMarkTint" id="0x7f0401e7" />
    <public type="attr" name="tickMarkTintMode" id="0x7f0401e8" />
    <public type="attr" name="tint" id="0x7f0401e9" />
    <public type="attr" name="tintMode" id="0x7f0401ea" />
    <public type="attr" name="title" id="0x7f0401eb" />
    <public type="attr" name="titleEnabled" id="0x7f0401ec" />
    <public type="attr" name="titleMargin" id="0x7f0401ed" />
    <public type="attr" name="titleMarginBottom" id="0x7f0401ee" />
    <public type="attr" name="titleMarginEnd" id="0x7f0401ef" />
    <public type="attr" name="titleMarginStart" id="0x7f0401f0" />
    <public type="attr" name="titleMarginTop" id="0x7f0401f1" />
    <public type="attr" name="titleMargins" id="0x7f0401f2" />
    <public type="attr" name="titleTextAppearance" id="0x7f0401f3" />
    <public type="attr" name="titleTextColor" id="0x7f0401f4" />
    <public type="attr" name="titleTextStyle" id="0x7f0401f5" />
    <public type="attr" name="toolbarId" id="0x7f0401f6" />
    <public type="attr" name="toolbarNavigationButtonStyle" id="0x7f0401f7" />
    <public type="attr" name="toolbarStyle" id="0x7f0401f8" />
    <public type="attr" name="tooltipForegroundColor" id="0x7f0401f9" />
    <public type="attr" name="tooltipFrameBackground" id="0x7f0401fa" />
    <public type="attr" name="tooltipText" id="0x7f0401fb" />
    <public type="attr" name="track" id="0x7f0401fc" />
    <public type="attr" name="trackTint" id="0x7f0401fd" />
    <public type="attr" name="trackTintMode" id="0x7f0401fe" />
    <public type="attr" name="ttcIndex" id="0x7f0401ff" />
    <public type="attr" name="useCompatPadding" id="0x7f040200" />
    <public type="attr" name="viewInflaterClass" id="0x7f040201" />
    <public type="attr" name="voiceIcon" id="0x7f040202" />
    <public type="attr" name="windowActionBar" id="0x7f040203" />
    <public type="attr" name="windowActionBarOverlay" id="0x7f040204" />
    <public type="attr" name="windowActionModeOverlay" id="0x7f040205" />
    <public type="attr" name="windowFixedHeightMajor" id="0x7f040206" />
    <public type="attr" name="windowFixedHeightMinor" id="0x7f040207" />
    <public type="attr" name="windowFixedWidthMajor" id="0x7f040208" />
    <public type="attr" name="windowFixedWidthMinor" id="0x7f040209" />
    <public type="attr" name="windowMinWidthMajor" id="0x7f04020a" />
    <public type="attr" name="windowMinWidthMinor" id="0x7f04020b" />
    <public type="attr" name="windowNoTitle" id="0x7f04020c" />
    <public type="bool" name="abc_action_bar_embed_tabs" id="0x7f050000" />
    <public type="bool" name="abc_allow_stacked_button_bar" id="0x7f050001" />
    <public type="bool" name="abc_config_actionMenuItemAllCaps" id="0x7f050002" />
    <public type="bool" name="mtrl_btn_textappearance_all_caps" id="0x7f050003" />
    <public type="color" name="abc_background_cache_hint_selector_material_dark" id="0x7f060000" />
    <public type="color" name="abc_background_cache_hint_selector_material_light" id="0x7f060001" />
    <public type="color" name="abc_btn_colored_borderless_text_material" id="0x7f060002" />
    <public type="color" name="abc_btn_colored_text_material" id="0x7f060003" />
    <public type="color" name="abc_hint_foreground_material_dark" id="0x7f060005" />
    <public type="color" name="abc_hint_foreground_material_light" id="0x7f060006" />
    <public type="color" name="abc_input_method_navigation_guard" id="0x7f060007" />
    <public type="color" name="abc_primary_text_disable_only_material_dark" id="0x7f060008" />
    <public type="color" name="abc_primary_text_disable_only_material_light" id="0x7f060009" />
    <public type="color" name="abc_primary_text_material_dark" id="0x7f06000a" />
    <public type="color" name="abc_primary_text_material_light" id="0x7f06000b" />
    <public type="color" name="abc_search_url_text" id="0x7f06000c" />
    <public type="color" name="abc_search_url_text_normal" id="0x7f06000d" />
    <public type="color" name="abc_search_url_text_pressed" id="0x7f06000e" />
    <public type="color" name="abc_search_url_text_selected" id="0x7f06000f" />
    <public type="color" name="abc_secondary_text_material_dark" id="0x7f060010" />
    <public type="color" name="abc_secondary_text_material_light" id="0x7f060011" />
    <public type="color" name="abc_tint_btn_checkable" id="0x7f060012" />
    <public type="color" name="abc_tint_default" id="0x7f060013" />
    <public type="color" name="abc_tint_edittext" id="0x7f060014" />
    <public type="color" name="abc_tint_seek_thumb" id="0x7f060015" />
    <public type="color" name="abc_tint_spinner" id="0x7f060016" />
    <public type="color" name="abc_tint_switch_track" id="0x7f060017" />
    <public type="color" name="accent_material_dark" id="0x7f060018" />
    <public type="color" name="accent_material_light" id="0x7f060019" />
    <public type="color" name="background_floating_material_dark" id="0x7f06001a" />
    <public type="color" name="background_floating_material_light" id="0x7f06001b" />
    <public type="color" name="background_material_dark" id="0x7f06001c" />
    <public type="color" name="background_material_light" id="0x7f06001d" />
    <public type="color" name="bright_foreground_disabled_material_dark" id="0x7f06001e" />
    <public type="color" name="bright_foreground_disabled_material_light" id="0x7f06001f" />
    <public type="color" name="bright_foreground_inverse_material_dark" id="0x7f060020" />
    <public type="color" name="bright_foreground_inverse_material_light" id="0x7f060021" />
    <public type="color" name="bright_foreground_material_dark" id="0x7f060022" />
    <public type="color" name="bright_foreground_material_light" id="0x7f060023" />
    <public type="color" name="button_material_dark" id="0x7f060024" />
    <public type="color" name="button_material_light" id="0x7f060025" />
    <public type="color" name="cardview_dark_background" id="0x7f060026" />
    <public type="color" name="cardview_light_background" id="0x7f060027" />
    <public type="color" name="cardview_shadow_end_color" id="0x7f060028" />
    <public type="color" name="cardview_shadow_start_color" id="0x7f060029" />
    <public type="color" name="colorAccent" id="0x7f06002a" />
    <public type="color" name="colorButton" id="0x7f06002b" />
    <public type="color" name="colorNetBackground" id="0x7f06002c" />
    <public type="color" name="colorNetControlBackground" id="0x7f06002d" />
    <public type="color" name="colorPrimary" id="0x7f06002e" />
    <public type="color" name="colorPrimaryDark" id="0x7f06002f" />
    <public type="color" name="design_bottom_navigation_shadow_color" id="0x7f060030" />
    <public type="color" name="design_default_color_primary" id="0x7f060031" />
    <public type="color" name="design_default_color_primary_dark" id="0x7f060032" />
    <public type="color" name="design_error" id="0x7f060033" />
    <public type="color" name="design_fab_shadow_end_color" id="0x7f060034" />
    <public type="color" name="design_fab_shadow_mid_color" id="0x7f060035" />
    <public type="color" name="design_fab_shadow_start_color" id="0x7f060036" />
    <public type="color" name="design_fab_stroke_end_inner_color" id="0x7f060037" />
    <public type="color" name="design_fab_stroke_end_outer_color" id="0x7f060038" />
    <public type="color" name="design_fab_stroke_top_inner_color" id="0x7f060039" />
    <public type="color" name="design_fab_stroke_top_outer_color" id="0x7f06003a" />
    <public type="color" name="design_snackbar_background_color" id="0x7f06003b" />
    <public type="color" name="design_tint_password_toggle" id="0x7f06003c" />
    <public type="color" name="dim_foreground_disabled_material_dark" id="0x7f06003d" />
    <public type="color" name="dim_foreground_disabled_material_light" id="0x7f06003e" />
    <public type="color" name="dim_foreground_material_dark" id="0x7f06003f" />
    <public type="color" name="dim_foreground_material_light" id="0x7f060040" />
    <public type="color" name="error_color_material_dark" id="0x7f060041" />
    <public type="color" name="error_color_material_light" id="0x7f060042" />
    <public type="color" name="foreground_material_dark" id="0x7f060043" />
    <public type="color" name="foreground_material_light" id="0x7f060044" />
    <public type="color" name="fw_bg_pressed" id="0x7f060045" />
    <public type="color" name="fw_bg_selected" id="0x7f060046" />
    <public type="color" name="fw_text_normal" id="0x7f060047" />
    <public type="color" name="fw_text_pressed" id="0x7f060048" />
    <public type="color" name="highlighted_text_material_dark" id="0x7f060049" />
    <public type="color" name="highlighted_text_material_light" id="0x7f06004a" />
    <public type="color" name="listview_text_color_selector" id="0x7f06004b" />
    <public type="color" name="material_blue_grey_800" id="0x7f06004c" />
    <public type="color" name="material_blue_grey_900" id="0x7f06004d" />
    <public type="color" name="material_blue_grey_950" id="0x7f06004e" />
    <public type="color" name="material_deep_teal_200" id="0x7f06004f" />
    <public type="color" name="material_deep_teal_500" id="0x7f060050" />
    <public type="color" name="material_grey_100" id="0x7f060051" />
    <public type="color" name="material_grey_300" id="0x7f060052" />
    <public type="color" name="material_grey_50" id="0x7f060053" />
    <public type="color" name="material_grey_600" id="0x7f060054" />
    <public type="color" name="material_grey_800" id="0x7f060055" />
    <public type="color" name="material_grey_850" id="0x7f060056" />
    <public type="color" name="material_grey_900" id="0x7f060057" />
    <public type="color" name="mtrl_bottom_nav_colored_item_tint" id="0x7f060058" />
    <public type="color" name="mtrl_bottom_nav_item_tint" id="0x7f060059" />
    <public type="color" name="mtrl_btn_bg_color_disabled" id="0x7f06005a" />
    <public type="color" name="mtrl_btn_bg_color_selector" id="0x7f06005b" />
    <public type="color" name="mtrl_btn_ripple_color" id="0x7f06005c" />
    <public type="color" name="mtrl_btn_stroke_color_selector" id="0x7f06005d" />
    <public type="color" name="mtrl_btn_text_btn_ripple_color" id="0x7f06005e" />
    <public type="color" name="mtrl_btn_text_color_disabled" id="0x7f06005f" />
    <public type="color" name="mtrl_btn_text_color_selector" id="0x7f060060" />
    <public type="color" name="mtrl_btn_transparent_bg_color" id="0x7f060061" />
    <public type="color" name="mtrl_chip_background_color" id="0x7f060062" />
    <public type="color" name="mtrl_chip_close_icon_tint" id="0x7f060063" />
    <public type="color" name="mtrl_chip_ripple_color" id="0x7f060064" />
    <public type="color" name="mtrl_chip_text_color" id="0x7f060065" />
    <public type="color" name="mtrl_fab_ripple_color" id="0x7f060066" />
    <public type="color" name="mtrl_scrim_color" id="0x7f060067" />
    <public type="color" name="mtrl_tabs_colored_ripple_color" id="0x7f060068" />
    <public type="color" name="mtrl_tabs_icon_color_selector" id="0x7f060069" />
    <public type="color" name="mtrl_tabs_icon_color_selector_colored" id="0x7f06006a" />
    <public type="color" name="mtrl_tabs_legacy_text_color_selector" id="0x7f06006b" />
    <public type="color" name="mtrl_tabs_ripple_color" id="0x7f06006c" />
    <public type="color" name="mtrl_text_btn_text_color_selector" id="0x7f06006d" />
    <public type="color" name="mtrl_textinput_default_box_stroke_color" id="0x7f06006e" />
    <public type="color" name="mtrl_textinput_disabled_color" id="0x7f06006f" />
    <public type="color" name="mtrl_textinput_filled_box_default_background_color" id="0x7f060070" />
    <public type="color" name="mtrl_textinput_hovered_box_stroke_color" id="0x7f060071" />
    <public type="color" name="notification_action_color_filter" id="0x7f060072" />
    <public type="color" name="notification_icon_bg_color" id="0x7f060073" />
    <public type="color" name="notification_material_background_media_default_color" id="0x7f060074" />
    <public type="color" name="primary_dark_material_dark" id="0x7f060075" />
    <public type="color" name="primary_dark_material_light" id="0x7f060076" />
    <public type="color" name="primary_material_dark" id="0x7f060077" />
    <public type="color" name="primary_material_light" id="0x7f060078" />
    <public type="color" name="primary_text_default_material_dark" id="0x7f060079" />
    <public type="color" name="primary_text_default_material_light" id="0x7f06007a" />
    <public type="color" name="primary_text_disabled_material_dark" id="0x7f06007b" />
    <public type="color" name="primary_text_disabled_material_light" id="0x7f06007c" />
    <public type="color" name="ripple_material_dark" id="0x7f06007d" />
    <public type="color" name="ripple_material_light" id="0x7f06007e" />
    <public type="color" name="secondary_text_default_material_dark" id="0x7f06007f" />
    <public type="color" name="secondary_text_default_material_light" id="0x7f060080" />
    <public type="color" name="secondary_text_disabled_material_dark" id="0x7f060081" />
    <public type="color" name="secondary_text_disabled_material_light" id="0x7f060082" />
    <public type="color" name="switch_thumb_disabled_material_dark" id="0x7f060083" />
    <public type="color" name="switch_thumb_disabled_material_light" id="0x7f060084" />
    <public type="color" name="switch_thumb_material_dark" id="0x7f060085" />
    <public type="color" name="switch_thumb_material_light" id="0x7f060086" />
    <public type="color" name="switch_thumb_normal_material_dark" id="0x7f060087" />
    <public type="color" name="switch_thumb_normal_material_light" id="0x7f060088" />
    <public type="color" name="tooltip_background_dark" id="0x7f060089" />
    <public type="color" name="tooltip_background_light" id="0x7f06008a" />
    <public type="color" name="abc_color_highlight_material" id="0x7f060004" />
    <public type="dimen" name="abc_action_bar_content_inset_material" id="0x7f070000" />
    <public type="dimen" name="abc_action_bar_content_inset_with_nav" id="0x7f070001" />
    <public type="dimen" name="abc_action_bar_default_height_material" id="0x7f070002" />
    <public type="dimen" name="abc_action_bar_default_padding_end_material" id="0x7f070003" />
    <public type="dimen" name="abc_action_bar_default_padding_start_material" id="0x7f070004" />
    <public type="dimen" name="abc_action_bar_elevation_material" id="0x7f070005" />
    <public type="dimen" name="abc_action_bar_icon_vertical_padding_material" id="0x7f070006" />
    <public type="dimen" name="abc_action_bar_overflow_padding_end_material" id="0x7f070007" />
    <public type="dimen" name="abc_action_bar_overflow_padding_start_material" id="0x7f070008" />
    <public type="dimen" name="abc_action_bar_stacked_max_height" id="0x7f070009" />
    <public type="dimen" name="abc_action_bar_stacked_tab_max_width" id="0x7f07000a" />
    <public type="dimen" name="abc_action_bar_subtitle_bottom_margin_material" id="0x7f07000b" />
    <public type="dimen" name="abc_action_bar_subtitle_top_margin_material" id="0x7f07000c" />
    <public type="dimen" name="abc_action_button_min_height_material" id="0x7f07000d" />
    <public type="dimen" name="abc_action_button_min_width_material" id="0x7f07000e" />
    <public type="dimen" name="abc_action_button_min_width_overflow_material" id="0x7f07000f" />
    <public type="dimen" name="abc_alert_dialog_button_bar_height" id="0x7f070010" />
    <public type="dimen" name="abc_alert_dialog_button_dimen" id="0x7f070011" />
    <public type="dimen" name="abc_button_inset_horizontal_material" id="0x7f070012" />
    <public type="dimen" name="abc_button_inset_vertical_material" id="0x7f070013" />
    <public type="dimen" name="abc_button_padding_horizontal_material" id="0x7f070014" />
    <public type="dimen" name="abc_button_padding_vertical_material" id="0x7f070015" />
    <public type="dimen" name="abc_cascading_menus_min_smallest_width" id="0x7f070016" />
    <public type="dimen" name="abc_config_prefDialogWidth" id="0x7f070017" />
    <public type="dimen" name="abc_control_corner_material" id="0x7f070018" />
    <public type="dimen" name="abc_control_inset_material" id="0x7f070019" />
    <public type="dimen" name="abc_control_padding_material" id="0x7f07001a" />
    <public type="dimen" name="abc_dialog_corner_radius_material" id="0x7f07001b" />
    <public type="dimen" name="abc_dialog_fixed_height_major" id="0x7f07001c" />
    <public type="dimen" name="abc_dialog_fixed_height_minor" id="0x7f07001d" />
    <public type="dimen" name="abc_dialog_fixed_width_major" id="0x7f07001e" />
    <public type="dimen" name="abc_dialog_fixed_width_minor" id="0x7f07001f" />
    <public type="dimen" name="abc_dialog_list_padding_bottom_no_buttons" id="0x7f070020" />
    <public type="dimen" name="abc_dialog_list_padding_top_no_title" id="0x7f070021" />
    <public type="dimen" name="abc_dialog_min_width_major" id="0x7f070022" />
    <public type="dimen" name="abc_dialog_min_width_minor" id="0x7f070023" />
    <public type="dimen" name="abc_dialog_padding_material" id="0x7f070024" />
    <public type="dimen" name="abc_dialog_padding_top_material" id="0x7f070025" />
    <public type="dimen" name="abc_dialog_title_divider_material" id="0x7f070026" />
    <public type="dimen" name="abc_disabled_alpha_material_dark" id="0x7f070027" />
    <public type="dimen" name="abc_disabled_alpha_material_light" id="0x7f070028" />
    <public type="dimen" name="abc_dropdownitem_icon_width" id="0x7f070029" />
    <public type="dimen" name="abc_dropdownitem_text_padding_left" id="0x7f07002a" />
    <public type="dimen" name="abc_dropdownitem_text_padding_right" id="0x7f07002b" />
    <public type="dimen" name="abc_edit_text_inset_bottom_material" id="0x7f07002c" />
    <public type="dimen" name="abc_edit_text_inset_horizontal_material" id="0x7f07002d" />
    <public type="dimen" name="abc_edit_text_inset_top_material" id="0x7f07002e" />
    <public type="dimen" name="abc_floating_window_z" id="0x7f07002f" />
    <public type="dimen" name="abc_list_item_padding_horizontal_material" id="0x7f070030" />
    <public type="dimen" name="abc_panel_menu_list_width" id="0x7f070031" />
    <public type="dimen" name="abc_progress_bar_height_material" id="0x7f070032" />
    <public type="dimen" name="abc_search_view_preferred_height" id="0x7f070033" />
    <public type="dimen" name="abc_search_view_preferred_width" id="0x7f070034" />
    <public type="dimen" name="abc_seekbar_track_background_height_material" id="0x7f070035" />
    <public type="dimen" name="abc_seekbar_track_progress_height_material" id="0x7f070036" />
    <public type="dimen" name="abc_select_dialog_padding_start_material" id="0x7f070037" />
    <public type="dimen" name="abc_switch_padding" id="0x7f070038" />
    <public type="dimen" name="abc_text_size_body_1_material" id="0x7f070039" />
    <public type="dimen" name="abc_text_size_body_2_material" id="0x7f07003a" />
    <public type="dimen" name="abc_text_size_button_material" id="0x7f07003b" />
    <public type="dimen" name="abc_text_size_caption_material" id="0x7f07003c" />
    <public type="dimen" name="abc_text_size_display_1_material" id="0x7f07003d" />
    <public type="dimen" name="abc_text_size_display_2_material" id="0x7f07003e" />
    <public type="dimen" name="abc_text_size_display_3_material" id="0x7f07003f" />
    <public type="dimen" name="abc_text_size_display_4_material" id="0x7f070040" />
    <public type="dimen" name="abc_text_size_headline_material" id="0x7f070041" />
    <public type="dimen" name="abc_text_size_large_material" id="0x7f070042" />
    <public type="dimen" name="abc_text_size_medium_material" id="0x7f070043" />
    <public type="dimen" name="abc_text_size_menu_header_material" id="0x7f070044" />
    <public type="dimen" name="abc_text_size_menu_material" id="0x7f070045" />
    <public type="dimen" name="abc_text_size_small_material" id="0x7f070046" />
    <public type="dimen" name="abc_text_size_subhead_material" id="0x7f070047" />
    <public type="dimen" name="abc_text_size_subtitle_material_toolbar" id="0x7f070048" />
    <public type="dimen" name="abc_text_size_title_material" id="0x7f070049" />
    <public type="dimen" name="abc_text_size_title_material_toolbar" id="0x7f07004a" />
    <public type="dimen" name="cardview_compat_inset_shadow" id="0x7f07004b" />
    <public type="dimen" name="cardview_default_elevation" id="0x7f07004c" />
    <public type="dimen" name="cardview_default_radius" id="0x7f07004d" />
    <public type="dimen" name="compat_button_inset_horizontal_material" id="0x7f07004e" />
    <public type="dimen" name="compat_button_inset_vertical_material" id="0x7f07004f" />
    <public type="dimen" name="compat_button_padding_horizontal_material" id="0x7f070050" />
    <public type="dimen" name="compat_button_padding_vertical_material" id="0x7f070051" />
    <public type="dimen" name="compat_control_corner_material" id="0x7f070052" />
    <public type="dimen" name="compat_notification_large_icon_max_height" id="0x7f070053" />
    <public type="dimen" name="compat_notification_large_icon_max_width" id="0x7f070054" />
    <public type="dimen" name="design_appbar_elevation" id="0x7f070055" />
    <public type="dimen" name="design_bottom_navigation_active_item_max_width" id="0x7f070056" />
    <public type="dimen" name="design_bottom_navigation_active_item_min_width" id="0x7f070057" />
    <public type="dimen" name="design_bottom_navigation_active_text_size" id="0x7f070058" />
    <public type="dimen" name="design_bottom_navigation_elevation" id="0x7f070059" />
    <public type="dimen" name="design_bottom_navigation_height" id="0x7f07005a" />
    <public type="dimen" name="design_bottom_navigation_icon_size" id="0x7f07005b" />
    <public type="dimen" name="design_bottom_navigation_item_max_width" id="0x7f07005c" />
    <public type="dimen" name="design_bottom_navigation_item_min_width" id="0x7f07005d" />
    <public type="dimen" name="design_bottom_navigation_margin" id="0x7f07005e" />
    <public type="dimen" name="design_bottom_navigation_shadow_height" id="0x7f07005f" />
    <public type="dimen" name="design_bottom_navigation_text_size" id="0x7f070060" />
    <public type="dimen" name="design_bottom_sheet_modal_elevation" id="0x7f070061" />
    <public type="dimen" name="design_bottom_sheet_peek_height_min" id="0x7f070062" />
    <public type="dimen" name="design_fab_border_width" id="0x7f070063" />
    <public type="dimen" name="design_fab_elevation" id="0x7f070064" />
    <public type="dimen" name="design_fab_image_size" id="0x7f070065" />
    <public type="dimen" name="design_fab_size_mini" id="0x7f070066" />
    <public type="dimen" name="design_fab_size_normal" id="0x7f070067" />
    <public type="dimen" name="design_fab_translation_z_hovered_focused" id="0x7f070068" />
    <public type="dimen" name="design_fab_translation_z_pressed" id="0x7f070069" />
    <public type="dimen" name="design_navigation_elevation" id="0x7f07006a" />
    <public type="dimen" name="design_navigation_icon_padding" id="0x7f07006b" />
    <public type="dimen" name="design_navigation_icon_size" id="0x7f07006c" />
    <public type="dimen" name="design_navigation_item_horizontal_padding" id="0x7f07006d" />
    <public type="dimen" name="design_navigation_item_icon_padding" id="0x7f07006e" />
    <public type="dimen" name="design_navigation_max_width" id="0x7f07006f" />
    <public type="dimen" name="design_navigation_padding_bottom" id="0x7f070070" />
    <public type="dimen" name="design_navigation_separator_vertical_padding" id="0x7f070071" />
    <public type="dimen" name="design_snackbar_action_inline_max_width" id="0x7f070072" />
    <public type="dimen" name="design_snackbar_background_corner_radius" id="0x7f070073" />
    <public type="dimen" name="design_snackbar_elevation" id="0x7f070074" />
    <public type="dimen" name="design_snackbar_extra_spacing_horizontal" id="0x7f070075" />
    <public type="dimen" name="design_snackbar_max_width" id="0x7f070076" />
    <public type="dimen" name="design_snackbar_min_width" id="0x7f070077" />
    <public type="dimen" name="design_snackbar_padding_horizontal" id="0x7f070078" />
    <public type="dimen" name="design_snackbar_padding_vertical" id="0x7f070079" />
    <public type="dimen" name="design_snackbar_padding_vertical_2lines" id="0x7f07007a" />
    <public type="dimen" name="design_snackbar_text_size" id="0x7f07007b" />
    <public type="dimen" name="design_tab_max_width" id="0x7f07007c" />
    <public type="dimen" name="design_tab_scrollable_min_width" id="0x7f07007d" />
    <public type="dimen" name="design_tab_text_size" id="0x7f07007e" />
    <public type="dimen" name="design_tab_text_size_2line" id="0x7f07007f" />
    <public type="dimen" name="design_textinput_caption_translate_y" id="0x7f070080" />
    <public type="dimen" name="disabled_alpha_material_dark" id="0x7f070081" />
    <public type="dimen" name="disabled_alpha_material_light" id="0x7f070082" />
    <public type="dimen" name="fab_margin" id="0x7f070083" />
    <public type="dimen" name="fastscroll__bubble_corner" id="0x7f070084" />
    <public type="dimen" name="fastscroll__bubble_size" id="0x7f070085" />
    <public type="dimen" name="fastscroll__handle_clickable_width" id="0x7f070086" />
    <public type="dimen" name="fastscroll__handle_corner" id="0x7f070087" />
    <public type="dimen" name="fastscroll__handle_height" id="0x7f070088" />
    <public type="dimen" name="fastscroll__handle_inset" id="0x7f070089" />
    <public type="dimen" name="fastscroll__handle_padding" id="0x7f07008a" />
    <public type="dimen" name="fastscroll__handle_width" id="0x7f07008b" />
    <public type="dimen" name="fastscroll_default_thickness" id="0x7f07008c" />
    <public type="dimen" name="fastscroll_margin" id="0x7f07008d" />
    <public type="dimen" name="fastscroll_minimum_range" id="0x7f07008e" />
    <public type="dimen" name="highlight_alpha_material_colored" id="0x7f07008f" />
    <public type="dimen" name="highlight_alpha_material_dark" id="0x7f070090" />
    <public type="dimen" name="highlight_alpha_material_light" id="0x7f070091" />
    <public type="dimen" name="hint_alpha_material_dark" id="0x7f070092" />
    <public type="dimen" name="hint_alpha_material_light" id="0x7f070093" />
    <public type="dimen" name="hint_pressed_alpha_material_dark" id="0x7f070094" />
    <public type="dimen" name="hint_pressed_alpha_material_light" id="0x7f070095" />
    <public type="dimen" name="item_touch_helper_max_drag_scroll_per_frame" id="0x7f070096" />
    <public type="dimen" name="item_touch_helper_swipe_escape_max_velocity" id="0x7f070097" />
    <public type="dimen" name="item_touch_helper_swipe_escape_velocity" id="0x7f070098" />
    <public type="dimen" name="mtrl_bottomappbar_fabOffsetEndMode" id="0x7f070099" />
    <public type="dimen" name="mtrl_bottomappbar_fab_cradle_margin" id="0x7f07009a" />
    <public type="dimen" name="mtrl_bottomappbar_fab_cradle_rounded_corner_radius" id="0x7f07009b" />
    <public type="dimen" name="mtrl_bottomappbar_fab_cradle_vertical_offset" id="0x7f07009c" />
    <public type="dimen" name="mtrl_bottomappbar_height" id="0x7f07009d" />
    <public type="dimen" name="mtrl_btn_corner_radius" id="0x7f07009e" />
    <public type="dimen" name="mtrl_btn_dialog_btn_min_width" id="0x7f07009f" />
    <public type="dimen" name="mtrl_btn_disabled_elevation" id="0x7f0700a0" />
    <public type="dimen" name="mtrl_btn_disabled_z" id="0x7f0700a1" />
    <public type="dimen" name="mtrl_btn_elevation" id="0x7f0700a2" />
    <public type="dimen" name="mtrl_btn_focused_z" id="0x7f0700a3" />
    <public type="dimen" name="mtrl_btn_hovered_z" id="0x7f0700a4" />
    <public type="dimen" name="mtrl_btn_icon_btn_padding_left" id="0x7f0700a5" />
    <public type="dimen" name="mtrl_btn_icon_padding" id="0x7f0700a6" />
    <public type="dimen" name="mtrl_btn_inset" id="0x7f0700a7" />
    <public type="dimen" name="mtrl_btn_letter_spacing" id="0x7f0700a8" />
    <public type="dimen" name="mtrl_btn_padding_bottom" id="0x7f0700a9" />
    <public type="dimen" name="mtrl_btn_padding_left" id="0x7f0700aa" />
    <public type="dimen" name="mtrl_btn_padding_right" id="0x7f0700ab" />
    <public type="dimen" name="mtrl_btn_padding_top" id="0x7f0700ac" />
    <public type="dimen" name="mtrl_btn_pressed_z" id="0x7f0700ad" />
    <public type="dimen" name="mtrl_btn_stroke_size" id="0x7f0700ae" />
    <public type="dimen" name="mtrl_btn_text_btn_icon_padding" id="0x7f0700af" />
    <public type="dimen" name="mtrl_btn_text_btn_padding_left" id="0x7f0700b0" />
    <public type="dimen" name="mtrl_btn_text_btn_padding_right" id="0x7f0700b1" />
    <public type="dimen" name="mtrl_btn_text_size" id="0x7f0700b2" />
    <public type="dimen" name="mtrl_btn_z" id="0x7f0700b3" />
    <public type="dimen" name="mtrl_card_elevation" id="0x7f0700b4" />
    <public type="dimen" name="mtrl_card_spacing" id="0x7f0700b5" />
    <public type="dimen" name="mtrl_chip_pressed_translation_z" id="0x7f0700b6" />
    <public type="dimen" name="mtrl_chip_text_size" id="0x7f0700b7" />
    <public type="dimen" name="mtrl_fab_elevation" id="0x7f0700b8" />
    <public type="dimen" name="mtrl_fab_translation_z_hovered_focused" id="0x7f0700b9" />
    <public type="dimen" name="mtrl_fab_translation_z_pressed" id="0x7f0700ba" />
    <public type="dimen" name="mtrl_navigation_elevation" id="0x7f0700bb" />
    <public type="dimen" name="mtrl_navigation_item_horizontal_padding" id="0x7f0700bc" />
    <public type="dimen" name="mtrl_navigation_item_icon_padding" id="0x7f0700bd" />
    <public type="dimen" name="mtrl_snackbar_background_corner_radius" id="0x7f0700be" />
    <public type="dimen" name="mtrl_snackbar_margin" id="0x7f0700bf" />
    <public type="dimen" name="mtrl_textinput_box_bottom_offset" id="0x7f0700c0" />
    <public type="dimen" name="mtrl_textinput_box_corner_radius_medium" id="0x7f0700c1" />
    <public type="dimen" name="mtrl_textinput_box_corner_radius_small" id="0x7f0700c2" />
    <public type="dimen" name="mtrl_textinput_box_label_cutout_padding" id="0x7f0700c3" />
    <public type="dimen" name="mtrl_textinput_box_padding_end" id="0x7f0700c4" />
    <public type="dimen" name="mtrl_textinput_box_stroke_width_default" id="0x7f0700c5" />
    <public type="dimen" name="mtrl_textinput_box_stroke_width_focused" id="0x7f0700c6" />
    <public type="dimen" name="mtrl_textinput_outline_box_expanded_padding" id="0x7f0700c7" />
    <public type="dimen" name="mtrl_toolbar_default_height" id="0x7f0700c8" />
    <public type="dimen" name="notification_action_icon_size" id="0x7f0700c9" />
    <public type="dimen" name="notification_action_text_size" id="0x7f0700ca" />
    <public type="dimen" name="notification_big_circle_margin" id="0x7f0700cb" />
    <public type="dimen" name="notification_content_margin_start" id="0x7f0700cc" />
    <public type="dimen" name="notification_large_icon_height" id="0x7f0700cd" />
    <public type="dimen" name="notification_large_icon_width" id="0x7f0700ce" />
    <public type="dimen" name="notification_main_column_padding_top" id="0x7f0700cf" />
    <public type="dimen" name="notification_media_narrow_margin" id="0x7f0700d0" />
    <public type="dimen" name="notification_right_icon_size" id="0x7f0700d1" />
    <public type="dimen" name="notification_right_side_padding_top" id="0x7f0700d2" />
    <public type="dimen" name="notification_small_icon_background_padding" id="0x7f0700d3" />
    <public type="dimen" name="notification_small_icon_size_as_large" id="0x7f0700d4" />
    <public type="dimen" name="notification_subtext_size" id="0x7f0700d5" />
    <public type="dimen" name="notification_top_pad" id="0x7f0700d6" />
    <public type="dimen" name="notification_top_pad_large_text" id="0x7f0700d7" />
    <public type="dimen" name="subtitle_corner_radius" id="0x7f0700d8" />
    <public type="dimen" name="subtitle_outline_width" id="0x7f0700d9" />
    <public type="dimen" name="subtitle_shadow_offset" id="0x7f0700da" />
    <public type="dimen" name="subtitle_shadow_radius" id="0x7f0700db" />
    <public type="dimen" name="tooltip_corner_radius" id="0x7f0700dc" />
    <public type="dimen" name="tooltip_horizontal_padding" id="0x7f0700dd" />
    <public type="dimen" name="tooltip_margin" id="0x7f0700de" />
    <public type="dimen" name="tooltip_precise_anchor_extra_offset" id="0x7f0700df" />
    <public type="dimen" name="tooltip_precise_anchor_threshold" id="0x7f0700e0" />
    <public type="dimen" name="tooltip_vertical_padding" id="0x7f0700e1" />
    <public type="dimen" name="tooltip_y_offset_non_touch" id="0x7f0700e2" />
    <public type="dimen" name="tooltip_y_offset_touch" id="0x7f0700e3" />
    <public type="dimen" name="tv_login_loading" id="0x7f0700e4" />
    <public type="drawable" name="$avd_hide_password__0" id="0x7f080000" />
    <public type="drawable" name="$avd_hide_password__1" id="0x7f080001" />
    <public type="drawable" name="$avd_hide_password__2" id="0x7f080002" />
    <public type="drawable" name="$avd_show_password__0" id="0x7f080003" />
    <public type="drawable" name="$avd_show_password__1" id="0x7f080004" />
    <public type="drawable" name="$avd_show_password__2" id="0x7f080005" />
    <public type="drawable" name="abc_action_bar_item_background_material" id="0x7f080008" />
    <public type="drawable" name="abc_btn_borderless_material" id="0x7f080009" />
    <public type="drawable" name="abc_btn_check_material" id="0x7f08000a" />
    <public type="drawable" name="abc_btn_colored_material" id="0x7f08000d" />
    <public type="drawable" name="abc_btn_default_mtrl_shape" id="0x7f08000e" />
    <public type="drawable" name="abc_btn_radio_material" id="0x7f08000f" />
    <public type="drawable" name="abc_cab_background_internal_bg" id="0x7f080014" />
    <public type="drawable" name="abc_cab_background_top_material" id="0x7f080015" />
    <public type="drawable" name="abc_dialog_material_background" id="0x7f080018" />
    <public type="drawable" name="abc_edit_text_material" id="0x7f080019" />
    <public type="drawable" name="abc_ic_ab_back_material" id="0x7f08001a" />
    <public type="drawable" name="abc_ic_arrow_drop_right_black_24dp" id="0x7f08001b" />
    <public type="drawable" name="abc_ic_clear_material" id="0x7f08001c" />
    <public type="drawable" name="abc_ic_go_search_api_material" id="0x7f08001e" />
    <public type="drawable" name="abc_ic_menu_overflow_material" id="0x7f080021" />
    <public type="drawable" name="abc_ic_search_api_material" id="0x7f080025" />
    <public type="drawable" name="abc_ic_voice_search_api_material" id="0x7f08002c" />
    <public type="drawable" name="abc_item_background_holo_dark" id="0x7f08002d" />
    <public type="drawable" name="abc_item_background_holo_light" id="0x7f08002e" />
    <public type="drawable" name="abc_list_divider_material" id="0x7f08002f" />
    <public type="drawable" name="abc_list_selector_background_transition_holo_dark" id="0x7f080035" />
    <public type="drawable" name="abc_list_selector_background_transition_holo_light" id="0x7f080036" />
    <public type="drawable" name="abc_list_selector_holo_dark" id="0x7f080039" />
    <public type="drawable" name="abc_list_selector_holo_light" id="0x7f08003a" />
    <public type="drawable" name="abc_ratingbar_indicator_material" id="0x7f08003d" />
    <public type="drawable" name="abc_ratingbar_material" id="0x7f08003e" />
    <public type="drawable" name="abc_ratingbar_small_material" id="0x7f08003f" />
    <public type="drawable" name="abc_seekbar_thumb_material" id="0x7f080045" />
    <public type="drawable" name="abc_seekbar_tick_mark_material" id="0x7f080046" />
    <public type="drawable" name="abc_seekbar_track_material" id="0x7f080047" />
    <public type="drawable" name="abc_spinner_textfield_background_material" id="0x7f080049" />
    <public type="drawable" name="abc_switch_thumb_material" id="0x7f08004a" />
    <public type="drawable" name="abc_tab_indicator_material" id="0x7f08004c" />
    <public type="drawable" name="abc_text_cursor_material" id="0x7f08004e" />
    <public type="drawable" name="abc_textfield_search_material" id="0x7f080059" />
    <public type="drawable" name="abc_vector_test" id="0x7f08005a" />
    <public type="drawable" name="avd_hide_password" id="0x7f08005b" />
    <public type="drawable" name="avd_show_password" id="0x7f08005c" />
    <public type="drawable" name="design_bottom_navigation_item_background" id="0x7f08005d" />
    <public type="drawable" name="design_fab_background" id="0x7f08005e" />
    <public type="drawable" name="design_password_eye" id="0x7f080061" />
    <public type="drawable" name="design_snackbar_background" id="0x7f080062" />
    <public type="drawable" name="fastscroll__default_bubble" id="0x7f080063" />
    <public type="drawable" name="fastscroll__default_handle" id="0x7f080064" />
    <public type="drawable" name="fw_listview_color_selector" id="0x7f080065" />
    <public type="drawable" name="head_default1" id="0x7f080066" />
    <public type="drawable" name="ic_expand_less" id="0x7f080067" />
    <public type="drawable" name="ic_expand_more" id="0x7f080068" />
    <public type="drawable" name="ic_image_edit" id="0x7f080069" />
    <public type="drawable" name="ic_launcher_background" id="0x7f08006a" />
    <public type="drawable" name="ic_mtrl_chip_checked_black" id="0x7f08006c" />
    <public type="drawable" name="ic_mtrl_chip_checked_circle" id="0x7f08006d" />
    <public type="drawable" name="ic_mtrl_chip_close_circle" id="0x7f08006e" />
    <public type="drawable" name="ic_pause" id="0x7f08006f" />
    <public type="drawable" name="ic_play_arrow" id="0x7f080070" />
    <public type="drawable" name="minimal_button_bg" id="0x7f080071" />
    <public type="drawable" name="layout_round_coner" id="0x7f080072" />
    <public type="drawable" name="mtrl_snackbar_background" id="0x7f080073" />
    <public type="drawable" name="mtrl_tabs_default_indicator" id="0x7f080074" />
    <public type="drawable" name="navigation_empty_icon" id="0x7f080075" />
    <public type="drawable" name="notification_action_background" id="0x7f080076" />
    <public type="drawable" name="notification_bg" id="0x7f080077" />
    <public type="drawable" name="notification_bg_low" id="0x7f080078" />
    <public type="drawable" name="notification_icon_background" id="0x7f08007d" />
    <public type="drawable" name="notification_template_icon_bg" id="0x7f08007e" />
    <public type="drawable" name="notification_template_icon_low_bg" id="0x7f08007f" />
    <public type="drawable" name="notification_tile_bg" id="0x7f080080" />
    <public type="drawable" name="qq_login" id="0x7f080082" />
    <public type="drawable" name="rtx_login" id="0x7f080083" />
    <public type="drawable" name="tooltip_frame_dark" id="0x7f080084" />
    <public type="drawable" name="tooltip_frame_light" id="0x7f080085" />
    <public type="drawable" name="minimal_card_bg" id="0x7f080086" />
    <public type="drawable" name="custom_icon" id="0x7f080087" />
    <public type="drawable" name="custom_icon_png" id="0x7f080088" />
    <public type="drawable" name="custom_image" id="0x7f080089" />
    <public type="drawable" name="circle_mask" id="0x7f08008a" />
    <public type="drawable" name="circle_image_mask" id="0x7f08008b" />
    <public type="drawable" name="custom_image_circle" id="0x7f08008c" />
    <public type="drawable" name="enhanced_button_bg" id="0x7f08008d" />
    <public type="drawable" name="meteor_particle" id="0x7f08008e" />
    <public type="drawable" name="meteor_trail" id="0x7f08008f" />
    <public type="drawable" name="meteor_background_gradient" id="0x7f080090" />
    <public type="drawable" name="simple_star" id="0x7f080091" />
    <public type="drawable" name="abc_control_background_material" id="0x7f080017" />
    <public type="drawable" name="$ic_launcher_foreground__0" id="0x7f080006" />
    <public type="drawable" name="ic_launcher_foreground" id="0x7f08006b" />
    <public type="drawable" name="abc_ab_share_pack_mtrl_alpha" id="0x7f080007" />
    <public type="drawable" name="abc_btn_check_to_on_mtrl_000" id="0x7f08000b" />
    <public type="drawable" name="abc_btn_check_to_on_mtrl_015" id="0x7f08000c" />
    <public type="drawable" name="abc_btn_radio_to_on_mtrl_000" id="0x7f080010" />
    <public type="drawable" name="abc_btn_radio_to_on_mtrl_015" id="0x7f080011" />
    <public type="drawable" name="abc_btn_switch_to_on_mtrl_00001" id="0x7f080012" />
    <public type="drawable" name="abc_btn_switch_to_on_mtrl_00012" id="0x7f080013" />
    <public type="drawable" name="abc_cab_background_top_mtrl_alpha" id="0x7f080016" />
    <public type="drawable" name="abc_ic_commit_search_api_mtrl_alpha" id="0x7f08001d" />
    <public type="drawable" name="abc_ic_menu_copy_mtrl_am_alpha" id="0x7f08001f" />
    <public type="drawable" name="abc_ic_menu_cut_mtrl_alpha" id="0x7f080020" />
    <public type="drawable" name="abc_ic_menu_paste_mtrl_am_alpha" id="0x7f080022" />
    <public type="drawable" name="abc_ic_menu_selectall_mtrl_alpha" id="0x7f080023" />
    <public type="drawable" name="abc_ic_menu_share_mtrl_alpha" id="0x7f080024" />
    <public type="drawable" name="abc_ic_star_black_16dp" id="0x7f080026" />
    <public type="drawable" name="abc_ic_star_black_36dp" id="0x7f080027" />
    <public type="drawable" name="abc_ic_star_black_48dp" id="0x7f080028" />
    <public type="drawable" name="abc_ic_star_half_black_16dp" id="0x7f080029" />
    <public type="drawable" name="abc_ic_star_half_black_36dp" id="0x7f08002a" />
    <public type="drawable" name="abc_ic_star_half_black_48dp" id="0x7f08002b" />
    <public type="drawable" name="abc_list_divider_mtrl_alpha" id="0x7f080030" />
    <public type="drawable" name="abc_list_focused_holo" id="0x7f080031" />
    <public type="drawable" name="abc_list_longpressed_holo" id="0x7f080032" />
    <public type="drawable" name="abc_list_pressed_holo_dark" id="0x7f080033" />
    <public type="drawable" name="abc_list_pressed_holo_light" id="0x7f080034" />
    <public type="drawable" name="abc_list_selector_disabled_holo_dark" id="0x7f080037" />
    <public type="drawable" name="abc_list_selector_disabled_holo_light" id="0x7f080038" />
    <public type="drawable" name="abc_menu_hardkey_panel_mtrl_mult" id="0x7f08003b" />
    <public type="drawable" name="abc_popup_background_mtrl_mult" id="0x7f08003c" />
    <public type="drawable" name="abc_scrubber_control_off_mtrl_alpha" id="0x7f080040" />
    <public type="drawable" name="abc_scrubber_control_to_pressed_mtrl_000" id="0x7f080041" />
    <public type="drawable" name="abc_scrubber_control_to_pressed_mtrl_005" id="0x7f080042" />
    <public type="drawable" name="abc_scrubber_primary_mtrl_alpha" id="0x7f080043" />
    <public type="drawable" name="abc_scrubber_track_mtrl_alpha" id="0x7f080044" />
    <public type="drawable" name="abc_spinner_mtrl_am_alpha" id="0x7f080048" />
    <public type="drawable" name="abc_switch_track_mtrl_alpha" id="0x7f08004b" />
    <public type="drawable" name="abc_tab_indicator_mtrl_alpha" id="0x7f08004d" />
    <public type="drawable" name="abc_text_select_handle_left_mtrl_dark" id="0x7f08004f" />
    <public type="drawable" name="abc_text_select_handle_left_mtrl_light" id="0x7f080050" />
    <public type="drawable" name="abc_text_select_handle_middle_mtrl_dark" id="0x7f080051" />
    <public type="drawable" name="abc_text_select_handle_middle_mtrl_light" id="0x7f080052" />
    <public type="drawable" name="abc_text_select_handle_right_mtrl_dark" id="0x7f080053" />
    <public type="drawable" name="abc_text_select_handle_right_mtrl_light" id="0x7f080054" />
    <public type="drawable" name="abc_textfield_activated_mtrl_alpha" id="0x7f080055" />
    <public type="drawable" name="abc_textfield_default_mtrl_alpha" id="0x7f080056" />
    <public type="drawable" name="abc_textfield_search_activated_mtrl_alpha" id="0x7f080057" />
    <public type="drawable" name="abc_textfield_search_default_mtrl_alpha" id="0x7f080058" />
    <public type="drawable" name="design_ic_visibility" id="0x7f08005f" />
    <public type="drawable" name="design_ic_visibility_off" id="0x7f080060" />
    <public type="drawable" name="notification_bg_low_normal" id="0x7f080079" />
    <public type="drawable" name="notification_bg_low_pressed" id="0x7f08007a" />
    <public type="drawable" name="notification_bg_normal" id="0x7f08007b" />
    <public type="drawable" name="notification_bg_normal_pressed" id="0x7f08007c" />
    <public type="drawable" name="notify_panel_notification_icon_bg" id="0x7f080081" />
    <public type="id" name="ALT" id="0x7f090000" />
    <public type="id" name="CTRL" id="0x7f090001" />
    <public type="id" name="FUNCTION" id="0x7f090002" />
    <public type="id" name="META" id="0x7f090003" />
    <public type="id" name="SHIFT" id="0x7f090004" />
    <public type="id" name="SYM" id="0x7f090005" />
    <public type="id" name="action0" id="0x7f090006" />
    <public type="id" name="action_bar" id="0x7f090007" />
    <public type="id" name="action_bar_activity_content" id="0x7f090008" />
    <public type="id" name="action_bar_container" id="0x7f090009" />
    <public type="id" name="action_bar_root" id="0x7f09000a" />
    <public type="id" name="action_bar_spinner" id="0x7f09000b" />
    <public type="id" name="action_bar_subtitle" id="0x7f09000c" />
    <public type="id" name="action_bar_title" id="0x7f09000d" />
    <public type="id" name="action_container" id="0x7f09000e" />
    <public type="id" name="action_context_bar" id="0x7f09000f" />
    <public type="id" name="action_divider" id="0x7f090010" />
    <public type="id" name="action_image" id="0x7f090011" />
    <public type="id" name="action_menu_divider" id="0x7f090012" />
    <public type="id" name="action_menu_presenter" id="0x7f090013" />
    <public type="id" name="action_mode_bar" id="0x7f090014" />
    <public type="id" name="action_mode_bar_stub" id="0x7f090015" />
    <public type="id" name="action_mode_close_button" id="0x7f090016" />
    <public type="id" name="action_text" id="0x7f090017" />
    <public type="id" name="actions" id="0x7f090018" />
    <public type="id" name="activity_chooser_view_content" id="0x7f090019" />
    <public type="id" name="add" id="0x7f09001a" />
    <public type="id" name="alertTitle" id="0x7f09001b" />
    <public type="id" name="all" id="0x7f09001c" />
    <public type="id" name="always" id="0x7f09001d" />
    <public type="id" name="async" id="0x7f09001e" />
    <public type="id" name="auto" id="0x7f09001f" />
    <public type="id" name="basic" id="0x7f090020" />
    <public type="id" name="beginning" id="0x7f090021" />
    <public type="id" name="blocking" id="0x7f090022" />
    <public type="id" name="bottom" id="0x7f090023" />
    <public type="id" name="btnAddProfile" id="0x7f090024" />
    <public type="id" name="btn_delete" id="0x7f090025" />
    <public type="id" name="btn_save" id="0x7f090026" />
    <public type="id" name="buttonPanel" id="0x7f090027" />
    <public type="id" name="buttonTest" id="0x7f090028" />
    <public type="id" name="cancel_action" id="0x7f090029" />
    <public type="id" name="center" id="0x7f09002a" />
    <public type="id" name="center_horizontal" id="0x7f09002b" />
    <public type="id" name="center_vertical" id="0x7f09002c" />
    <public type="id" name="chains" id="0x7f09002d" />
    <public type="id" name="checkbox" id="0x7f09002e" />
    <public type="id" name="chronometer" id="0x7f09002f" />
    <public type="id" name="clip_horizontal" id="0x7f090030" />
    <public type="id" name="clip_vertical" id="0x7f090031" />
    <public type="id" name="collapseActionView" id="0x7f090032" />
    <public type="id" name="container" id="0x7f090033" />
    <public type="id" name="content" id="0x7f090034" />
    <public type="id" name="contentPanel" id="0x7f090035" />
    <public type="id" name="coordinator" id="0x7f090036" />
    <public type="id" name="custom" id="0x7f090037" />
    <public type="id" name="customPanel" id="0x7f090038" />
    <public type="id" name="decor_content_parent" id="0x7f090039" />
    <public type="id" name="default_activity_button" id="0x7f09003a" />
    <public type="id" name="design_bottom_sheet" id="0x7f09003b" />
    <public type="id" name="design_menu_item_action_area" id="0x7f09003c" />
    <public type="id" name="design_menu_item_action_area_stub" id="0x7f09003d" />
    <public type="id" name="design_menu_item_text" id="0x7f09003e" />
    <public type="id" name="design_navigation_view" id="0x7f09003f" />
    <public type="id" name="disableHome" id="0x7f090040" />
    <public type="id" name="editText" id="0x7f090041" />
    <public type="id" name="editTextValue" id="0x7f090042" />
    <public type="id" name="editValue" id="0x7f090043" />
    <public type="id" name="edit_drop" id="0x7f090044" />
    <public type="id" name="edit_name" id="0x7f090045" />
    <public type="id" name="edit_pass" id="0x7f090046" />
    <public type="id" name="edit_query" id="0x7f090047" />
    <public type="id" name="end" id="0x7f090048" />
    <public type="id" name="end_padder" id="0x7f090049" />
    <public type="id" name="enterAlways" id="0x7f09004a" />
    <public type="id" name="enterAlwaysCollapsed" id="0x7f09004b" />
    <public type="id" name="exitUntilCollapsed" id="0x7f09004c" />
    <public type="id" name="expand_activities_button" id="0x7f09004d" />
    <public type="id" name="expanded_menu" id="0x7f09004e" />
    <public type="id" name="fastscroller" id="0x7f09004f" />
    <public type="id" name="fill" id="0x7f090050" />
    <public type="id" name="fill_horizontal" id="0x7f090051" />
    <public type="id" name="fill_vertical" id="0x7f090052" />
    <public type="id" name="filled" id="0x7f090053" />
    <public type="id" name="fixed" id="0x7f090054" />
    <public type="id" name="forever" id="0x7f090055" />
    <public type="id" name="fw_info_root" id="0x7f090056" />
    <public type="id" name="fw_root" id="0x7f090057" />
    <public type="id" name="ghost_view" id="0x7f090058" />
    <public type="id" name="group_divider" id="0x7f090059" />
    <public type="id" name="home" id="0x7f09005a" />
    <public type="id" name="homeAsUp" id="0x7f09005b" />
    <public type="id" name="icon" id="0x7f09005c" />
    <public type="id" name="icon_group" id="0x7f09005d" />
    <public type="id" name="ifRoom" id="0x7f09005e" />
    <public type="id" name="image" id="0x7f09005f" />
    <public type="id" name="imageViewPkg" id="0x7f090060" />
    <public type="id" name="img_expand" id="0x7f090061" />
    <public type="id" name="img_icon" id="0x7f090062" />
    <public type="id" name="img_start" id="0x7f090063" />
    <public type="id" name="info" id="0x7f090064" />
    <public type="id" name="italic" id="0x7f090065" />
    <public type="id" name="item_touch_helper_previous_elevation" id="0x7f090066" />
    <public type="id" name="itemicon" id="0x7f090067" />
    <public type="id" name="labeled" id="0x7f090068" />
    <public type="id" name="largeLabel" id="0x7f090069" />
    <public type="id" name="layoutProfileName" id="0x7f09006a" />
    <public type="id" name="layout_process" id="0x7f09006b" />
    <public type="id" name="lbl_main_ip" id="0x7f09006c" />
    <public type="id" name="left" id="0x7f09006d" />
    <public type="id" name="line1" id="0x7f09006e" />
    <public type="id" name="line3" id="0x7f09006f" />
    <public type="id" name="list" id="0x7f090070" />
    <public type="id" name="listMode" id="0x7f090071" />
    <public type="id" name="listViewEdit" id="0x7f090072" />
    <public type="id" name="list_item" id="0x7f090073" />
    <public type="id" name="loading" id="0x7f090074" />
    <public type="id" name="logout" id="0x7f090075" />
    <public type="id" name="lv_profileName" id="0x7f090076" />
    <public type="id" name="masked" id="0x7f090077" />
    <public type="id" name="media_actions" id="0x7f090078" />
    <public type="id" name="menu_login_out" id="0x7f090079" />
    <public type="id" name="menu_quit" id="0x7f09007a" />
    <public type="id" name="menu_settings" id="0x7f09007b" />
    <public type="id" name="message" id="0x7f09007c" />
    <public type="id" name="middle" id="0x7f09007d" />
    <public type="id" name="mini" id="0x7f09007e" />
    <public type="id" name="mtrl_child_content_container" id="0x7f09007f" />
    <public type="id" name="mtrl_internal_children_alpha_tag" id="0x7f090080" />
    <public type="id" name="multiply" id="0x7f090081" />
    <public type="id" name="navigation_header_container" id="0x7f090082" />
    <public type="id" name="net_config_list" id="0x7f090083" />
    <public type="id" name="net_edit_root" id="0x7f090084" />
    <public type="id" name="never" id="0x7f090085" />
    <public type="id" name="none" id="0x7f090086" />
    <public type="id" name="normal" id="0x7f090087" />
    <public type="id" name="notification_background" id="0x7f090088" />
    <public type="id" name="notification_main_column" id="0x7f090089" />
    <public type="id" name="notification_main_column_container" id="0x7f09008a" />
    <public type="id" name="oalogin" id="0x7f09008b" />
    <public type="id" name="outline" id="0x7f09008c" />
    <public type="id" name="packed" id="0x7f09008d" />
    <public type="id" name="parallax" id="0x7f09008e" />
    <public type="id" name="parent" id="0x7f09008f" />
    <public type="id" name="parentPanel" id="0x7f090090" />
    <public type="id" name="parent_matrix" id="0x7f090091" />
    <public type="id" name="pb_checkVersion" id="0x7f090092" />
    <public type="id" name="pin" id="0x7f090093" />
    <public type="id" name="progress_circular" id="0x7f090094" />
    <public type="id" name="progress_horizontal" id="0x7f090095" />
    <public type="id" name="qqlogin" id="0x7f090096" />
    <public type="id" name="radio" id="0x7f090097" />
    <public type="id" name="radiogroup_protocol" id="0x7f090098" />
    <public type="id" name="right" id="0x7f090099" />
    <public type="id" name="right_icon" id="0x7f09009a" />
    <public type="id" name="right_side" id="0x7f09009b" />
    <public type="id" name="rootActivityLayout" id="0x7f09009c" />
    <public type="id" name="save_image_matrix" id="0x7f09009d" />
    <public type="id" name="save_non_transition_alpha" id="0x7f09009e" />
    <public type="id" name="save_scale_type" id="0x7f09009f" />
    <public type="id" name="screen" id="0x7f0900a0" />
    <public type="id" name="scroll" id="0x7f0900a1" />
    <public type="id" name="scrollIndicatorDown" id="0x7f0900a2" />
    <public type="id" name="scrollIndicatorUp" id="0x7f0900a3" />
    <public type="id" name="scrollView" id="0x7f0900a4" />
    <public type="id" name="scrollable" id="0x7f0900a5" />
    <public type="id" name="search_badge" id="0x7f0900a6" />
    <public type="id" name="search_bar" id="0x7f0900a7" />
    <public type="id" name="search_button" id="0x7f0900a8" />
    <public type="id" name="search_close_btn" id="0x7f0900a9" />
    <public type="id" name="search_edit_frame" id="0x7f0900aa" />
    <public type="id" name="search_go_btn" id="0x7f0900ab" />
    <public type="id" name="search_mag_icon" id="0x7f0900ac" />
    <public type="id" name="search_plate" id="0x7f0900ad" />
    <public type="id" name="search_src_text" id="0x7f0900ae" />
    <public type="id" name="search_voice_btn" id="0x7f0900af" />
    <public type="id" name="select_dialog_listview" id="0x7f0900b0" />
    <public type="id" name="select_indicator" id="0x7f0900b1" />
    <public type="id" name="selected" id="0x7f0900b2" />
    <public type="id" name="shortcut" id="0x7f0900b3" />
    <public type="id" name="showCustom" id="0x7f0900b4" />
    <public type="id" name="showHome" id="0x7f0900b5" />
    <public type="id" name="showTitle" id="0x7f0900b6" />
    <public type="id" name="smallLabel" id="0x7f0900b7" />
    <public type="id" name="snackbar_action" id="0x7f0900b8" />
    <public type="id" name="snackbar_text" id="0x7f0900b9" />
    <public type="id" name="snap" id="0x7f0900ba" />
    <public type="id" name="snapMargins" id="0x7f0900bb" />
    <public type="id" name="spacer" id="0x7f0900bc" />
    <public type="id" name="split_action_bar" id="0x7f0900bd" />
    <public type="id" name="spread" id="0x7f0900be" />
    <public type="id" name="spread_inside" id="0x7f0900bf" />
    <public type="id" name="src_atop" id="0x7f0900c0" />
    <public type="id" name="src_in" id="0x7f0900c1" />
    <public type="id" name="src_over" id="0x7f0900c2" />
    <public type="id" name="start" id="0x7f0900c3" />
    <public type="id" name="status_bar_latest_event_content" id="0x7f0900c4" />
    <public type="id" name="stretch" id="0x7f0900c5" />
    <public type="id" name="submenuarrow" id="0x7f0900c6" />
    <public type="id" name="submit_area" id="0x7f0900c7" />
    <public type="id" name="tabMode" id="0x7f0900c8" />
    <public type="id" name="tableRowContent" id="0x7f0900c9" />
    <public type="id" name="tag_transition_group" id="0x7f0900ca" />
    <public type="id" name="tag_unhandled_key_event_manager" id="0x7f0900cb" />
    <public type="id" name="tag_unhandled_key_listeners" id="0x7f0900cc" />
    <public type="id" name="template_desc" id="0x7f0900cd" />
    <public type="id" name="template_edit" id="0x7f0900ce" />
    <public type="id" name="template_name" id="0x7f0900cf" />
    <public type="id" name="text" id="0x7f0900d0" />
    <public type="id" name="text2" id="0x7f0900d1" />
    <public type="id" name="textSpacerNoButtons" id="0x7f0900d2" />
    <public type="id" name="textSpacerNoTitle" id="0x7f0900d3" />
    <public type="id" name="textStart" id="0x7f0900d4" />
    <public type="id" name="textView1" id="0x7f0900d5" />
    <public type="id" name="textView2" id="0x7f0900d6" />
    <public type="id" name="textViewID" id="0x7f0900d7" />
    <public type="id" name="textViewName" id="0x7f0900d8" />
    <public type="id" name="textViewPkg" id="0x7f0900d9" />
    <public type="id" name="text_input_password_toggle" id="0x7f0900da" />
    <public type="id" name="textinput_counter" id="0x7f0900db" />
    <public type="id" name="textinput_error" id="0x7f0900dc" />
    <public type="id" name="textinput_helper_text" id="0x7f0900dd" />
    <public type="id" name="time" id="0x7f0900de" />
    <public type="id" name="title" id="0x7f0900df" />
    <public type="id" name="titleDividerNoCustom" id="0x7f0900e0" />
    <public type="id" name="title_template" id="0x7f0900e1" />
    <public type="id" name="toolbar" id="0x7f0900e2" />
    <public type="id" name="toolbar_title" id="0x7f0900e3" />
    <public type="id" name="top" id="0x7f0900e4" />
    <public type="id" name="topPanel" id="0x7f0900e5" />
    <public type="id" name="touch_outside" id="0x7f0900e6" />
    <public type="id" name="transition_current_scene" id="0x7f0900e7" />
    <public type="id" name="transition_layout_save" id="0x7f0900e8" />
    <public type="id" name="transition_position" id="0x7f0900e9" />
    <public type="id" name="transition_scene_layoutid_cache" id="0x7f0900ea" />
    <public type="id" name="transition_transform" id="0x7f0900eb" />
    <public type="id" name="tv_checkVersion" id="0x7f0900ec" />
    <public type="id" name="txtProfileName" id="0x7f0900ed" />
    <public type="id" name="txtVersion" id="0x7f0900ee" />
    <public type="id" name="txt_active" id="0x7f0900ef" />
    <public type="id" name="txt_info" id="0x7f0900f0" />
    <public type="id" name="txt_item" id="0x7f0900f1" />
    <public type="id" name="txt_ping" id="0x7f0900f2" />
    <public type="id" name="txt_profile" id="0x7f0900f3" />
    <public type="id" name="uniform" id="0x7f0900f4" />
    <public type="id" name="unlabeled" id="0x7f0900f5" />
    <public type="id" name="up" id="0x7f0900f6" />
    <public type="id" name="useLogo" id="0x7f0900f7" />
    <public type="id" name="user_pic" id="0x7f0900f8" />
    <public type="id" name="view_offset_helper" id="0x7f0900f9" />
    <public type="id" name="visible" id="0x7f0900fa" />
    <public type="id" name="webview" id="0x7f0900fb" />
    <public type="id" name="withText" id="0x7f0900fc" />
    <public type="id" name="wrap" id="0x7f0900fd" />
    <public type="id" name="wrap_content" id="0x7f0900fe" />
    <public type="integer" name="abc_config_activityDefaultDur" id="0x7f0a0000" />
    <public type="integer" name="abc_config_activityShortDur" id="0x7f0a0001" />
    <public type="integer" name="app_bar_elevation_anim_duration" id="0x7f0a0002" />
    <public type="integer" name="bottom_sheet_slide_duration" id="0x7f0a0003" />
    <public type="integer" name="cancel_button_image_alpha" id="0x7f0a0004" />
    <public type="integer" name="config_tooltipAnimTime" id="0x7f0a0005" />
    <public type="integer" name="design_snackbar_text_max_lines" id="0x7f0a0006" />
    <public type="integer" name="design_tab_indicator_anim_duration_ms" id="0x7f0a0007" />
    <public type="integer" name="hide_password_duration" id="0x7f0a0008" />
    <public type="integer" name="mtrl_btn_anim_delay_ms" id="0x7f0a0009" />
    <public type="integer" name="mtrl_btn_anim_duration_ms" id="0x7f0a000a" />
    <public type="integer" name="mtrl_chip_anim_duration" id="0x7f0a000b" />
    <public type="integer" name="mtrl_tab_indicator_anim_duration_ms" id="0x7f0a000c" />
    <public type="integer" name="show_password_duration" id="0x7f0a000d" />
    <public type="integer" name="status_bar_notification_info_maxnum" id="0x7f0a000e" />
    <public type="interpolator" name="mtrl_fast_out_linear_in" id="0x7f0b0000" />
    <public type="interpolator" name="mtrl_fast_out_slow_in" id="0x7f0b0001" />
    <public type="interpolator" name="mtrl_linear" id="0x7f0b0002" />
    <public type="interpolator" name="mtrl_linear_out_slow_in" id="0x7f0b0003" />
    <public type="layout" name="abc_action_bar_title_item" id="0x7f0c0000" />
    <public type="layout" name="abc_action_bar_up_container" id="0x7f0c0001" />
    <public type="layout" name="abc_action_menu_item_layout" id="0x7f0c0002" />
    <public type="layout" name="abc_action_menu_layout" id="0x7f0c0003" />
    <public type="layout" name="abc_action_mode_bar" id="0x7f0c0004" />
    <public type="layout" name="abc_action_mode_close_item_material" id="0x7f0c0005" />
    <public type="layout" name="abc_activity_chooser_view" id="0x7f0c0006" />
    <public type="layout" name="abc_activity_chooser_view_list_item" id="0x7f0c0007" />
    <public type="layout" name="abc_alert_dialog_button_bar_material" id="0x7f0c0008" />
    <public type="layout" name="abc_alert_dialog_material" id="0x7f0c0009" />
    <public type="layout" name="abc_alert_dialog_title_material" id="0x7f0c000a" />
    <public type="layout" name="abc_cascading_menu_item_layout" id="0x7f0c000b" />
    <public type="layout" name="abc_dialog_title_material" id="0x7f0c000c" />
    <public type="layout" name="abc_expanded_menu_layout" id="0x7f0c000d" />
    <public type="layout" name="abc_list_menu_item_checkbox" id="0x7f0c000e" />
    <public type="layout" name="abc_list_menu_item_icon" id="0x7f0c000f" />
    <public type="layout" name="abc_list_menu_item_layout" id="0x7f0c0010" />
    <public type="layout" name="abc_list_menu_item_radio" id="0x7f0c0011" />
    <public type="layout" name="abc_popup_menu_header_item_layout" id="0x7f0c0012" />
    <public type="layout" name="abc_popup_menu_item_layout" id="0x7f0c0013" />
    <public type="layout" name="abc_screen_content_include" id="0x7f0c0014" />
    <public type="layout" name="abc_screen_simple" id="0x7f0c0015" />
    <public type="layout" name="abc_screen_simple_overlay_action_mode" id="0x7f0c0016" />
    <public type="layout" name="abc_screen_toolbar" id="0x7f0c0017" />
    <public type="layout" name="abc_search_dropdown_item_icons_2line" id="0x7f0c0018" />
    <public type="layout" name="abc_search_view" id="0x7f0c0019" />
    <public type="layout" name="abc_select_dialog_material" id="0x7f0c001a" />
    <public type="layout" name="abc_tooltip" id="0x7f0c001b" />
    <public type="layout" name="activity_login" id="0x7f0c001c" />
    <public type="layout" name="activity_main" id="0x7f0c001d" />
    <public type="layout" name="activity_net_edit" id="0x7f0c001e" />
    <public type="layout" name="activity_root" id="0x7f0c001f" />
    <public type="layout" name="activity_web_oalogin" id="0x7f0c0020" />
    <public type="layout" name="activity_web_qqlogin" id="0x7f0c0021" />
    <public type="layout" name="activity_wetest_main" id="0x7f0c0022" />
    <public type="layout" name="content_main" id="0x7f0c0023" />
    <public type="layout" name="design_bottom_navigation_item" id="0x7f0c0024" />
    <public type="layout" name="design_bottom_sheet_dialog" id="0x7f0c0025" />
    <public type="layout" name="design_layout_snackbar" id="0x7f0c0026" />
    <public type="layout" name="design_layout_snackbar_include" id="0x7f0c0027" />
    <public type="layout" name="design_layout_tab_icon" id="0x7f0c0028" />
    <public type="layout" name="design_layout_tab_text" id="0x7f0c0029" />
    <public type="layout" name="design_menu_item_action_area" id="0x7f0c002a" />
    <public type="layout" name="design_navigation_item" id="0x7f0c002b" />
    <public type="layout" name="design_navigation_item_header" id="0x7f0c002c" />
    <public type="layout" name="design_navigation_item_separator" id="0x7f0c002d" />
    <public type="layout" name="design_navigation_item_subheader" id="0x7f0c002e" />
    <public type="layout" name="design_navigation_menu" id="0x7f0c002f" />
    <public type="layout" name="design_navigation_menu_item" id="0x7f0c0030" />
    <public type="layout" name="design_text_input_password_icon" id="0x7f0c0031" />
    <public type="layout" name="fastscroll__default_bubble" id="0x7f0c0032" />
    <public type="layout" name="layout_apps" id="0x7f0c0033" />
    <public type="layout" name="layout_apps_item" id="0x7f0c0034" />
    <public type="layout" name="layout_content" id="0x7f0c0035" />
    <public type="layout" name="layout_dialog_condrop" id="0x7f0c0036" />
    <public type="layout" name="layout_dialog_edit" id="0x7f0c0037" />
    <public type="layout" name="layout_dialog_protocol" id="0x7f0c0038" />
    <public type="layout" name="layout_float_window" id="0x7f0c0039" />
    <public type="layout" name="layout_float_window_expand" id="0x7f0c003a" />
    <public type="layout" name="layout_fw_listview_item" id="0x7f0c003c" />
    <public type="layout" name="layout_net_con_drop_item" id="0x7f0c003d" />
    <public type="layout" name="layout_net_edit_item" id="0x7f0c003e" />
    <public type="layout" name="layout_net_protocol_item" id="0x7f0c003f" />
    <public type="layout" name="layout_template_item" id="0x7f0c0040" />
    <public type="layout" name="mtrl_layout_snackbar" id="0x7f0c0041" />
    <public type="layout" name="mtrl_layout_snackbar_include" id="0x7f0c0042" />
    <public type="layout" name="notification_action" id="0x7f0c0043" />
    <public type="layout" name="notification_action_tombstone" id="0x7f0c0044" />
    <public type="layout" name="notification_media_action" id="0x7f0c0045" />
    <public type="layout" name="notification_media_cancel_action" id="0x7f0c0046" />
    <public type="layout" name="notification_template_big_media" id="0x7f0c0047" />
    <public type="layout" name="notification_template_big_media_custom" id="0x7f0c0048" />
    <public type="layout" name="notification_template_big_media_narrow" id="0x7f0c0049" />
    <public type="layout" name="notification_template_big_media_narrow_custom" id="0x7f0c004a" />
    <public type="layout" name="notification_template_custom_big" id="0x7f0c004b" />
    <public type="layout" name="notification_template_icon_group" id="0x7f0c004c" />
    <public type="layout" name="notification_template_lines_media" id="0x7f0c004d" />
    <public type="layout" name="notification_template_media" id="0x7f0c004e" />
    <public type="layout" name="notification_template_media_custom" id="0x7f0c004f" />
    <public type="layout" name="notification_template_part_chronometer" id="0x7f0c0050" />
    <public type="layout" name="notification_template_part_time" id="0x7f0c0051" />
    <public type="layout" name="select_dialog_item_material" id="0x7f0c0052" />
    <public type="layout" name="select_dialog_multichoice_material" id="0x7f0c0053" />
    <public type="layout" name="select_dialog_singlechoice_material" id="0x7f0c0054" />
    <public type="layout" name="support_simple_spinner_dropdown_item" id="0x7f0c0055" />
    <public type="layout" name="dynamic_meteor_background" id="0x7f0c0056" />
    <public type="layout" name="simple_meteor_background" id="0x7f0c0057" />
    <public type="menu" name="menu_main" id="0x7f0d0000" />
    <public type="mipmap" name="ic_launcher" id="0x7f0e0000" />
    <public type="mipmap" name="ic_launcher_round" id="0x7f0e0001" />
    <public type="raw" name="config" id="0x7f0f0000" />
    <public type="raw" name="ipmask" id="0x7f0f0001" />
    <public type="string" name="abc_action_bar_home_description" id="0x7f100000" />
    <public type="string" name="abc_action_bar_up_description" id="0x7f100001" />
    <public type="string" name="abc_action_menu_overflow_description" id="0x7f100002" />
    <public type="string" name="abc_action_mode_done" id="0x7f100003" />
    <public type="string" name="abc_activity_chooser_view_see_all" id="0x7f100004" />
    <public type="string" name="abc_activitychooserview_choose_application" id="0x7f100005" />
    <public type="string" name="abc_capital_off" id="0x7f100006" />
    <public type="string" name="abc_capital_on" id="0x7f100007" />
    <public type="string" name="abc_font_family_body_1_material" id="0x7f100008" />
    <public type="string" name="abc_font_family_body_2_material" id="0x7f100009" />
    <public type="string" name="abc_font_family_button_material" id="0x7f10000a" />
    <public type="string" name="abc_font_family_caption_material" id="0x7f10000b" />
    <public type="string" name="abc_font_family_display_1_material" id="0x7f10000c" />
    <public type="string" name="abc_font_family_display_2_material" id="0x7f10000d" />
    <public type="string" name="abc_font_family_display_3_material" id="0x7f10000e" />
    <public type="string" name="abc_font_family_display_4_material" id="0x7f10000f" />
    <public type="string" name="abc_font_family_headline_material" id="0x7f100010" />
    <public type="string" name="abc_font_family_menu_material" id="0x7f100011" />
    <public type="string" name="abc_font_family_subhead_material" id="0x7f100012" />
    <public type="string" name="abc_font_family_title_material" id="0x7f100013" />
    <public type="string" name="abc_menu_alt_shortcut_label" id="0x7f100014" />
    <public type="string" name="abc_menu_ctrl_shortcut_label" id="0x7f100015" />
    <public type="string" name="abc_menu_delete_shortcut_label" id="0x7f100016" />
    <public type="string" name="abc_menu_enter_shortcut_label" id="0x7f100017" />
    <public type="string" name="abc_menu_function_shortcut_label" id="0x7f100018" />
    <public type="string" name="abc_menu_meta_shortcut_label" id="0x7f100019" />
    <public type="string" name="abc_menu_shift_shortcut_label" id="0x7f10001a" />
    <public type="string" name="abc_menu_space_shortcut_label" id="0x7f10001b" />
    <public type="string" name="abc_menu_sym_shortcut_label" id="0x7f10001c" />
    <public type="string" name="abc_prepend_shortcut_label" id="0x7f10001d" />
    <public type="string" name="abc_search_hint" id="0x7f10001e" />
    <public type="string" name="abc_searchview_description_clear" id="0x7f10001f" />
    <public type="string" name="abc_searchview_description_query" id="0x7f100020" />
    <public type="string" name="abc_searchview_description_search" id="0x7f100021" />
    <public type="string" name="abc_searchview_description_submit" id="0x7f100022" />
    <public type="string" name="abc_searchview_description_voice" id="0x7f100023" />
    <public type="string" name="abc_shareactionprovider_share_with" id="0x7f100024" />
    <public type="string" name="abc_shareactionprovider_share_with_application" id="0x7f100025" />
    <public type="string" name="abc_toolbar_collapse_description" id="0x7f100026" />
    <public type="string" name="about_title" id="0x7f100027" />
    <public type="string" name="action_settings" id="0x7f100028" />
    <public type="string" name="action_title" id="0x7f100029" />
    <public type="string" name="app_name" id="0x7f10002a" />
    <public type="string" name="app_version" id="0x7f10002b" />
    <public type="string" name="appbar_scrolling_view_behavior" id="0x7f10002c" />
    <public type="string" name="author" id="0x7f10002d" />
    <public type="string" name="bottom_sheet_behavior" id="0x7f10002e" />
    <public type="string" name="btn_add_profile" id="0x7f10002f" />
    <public type="string" name="button_delete_profile" id="0x7f100030" />
    <public type="string" name="button_save_profile" id="0x7f100031" />
    <public type="string" name="button_start_test" id="0x7f100032" />
    <public type="string" name="button_stop_test" id="0x7f100033" />
    <public type="string" name="character_counter_content_description" id="0x7f100034" />
    <public type="string" name="character_counter_pattern" id="0x7f100035" />
    <public type="string" name="checkVersionTips" id="0x7f100036" />
    <public type="string" name="default_profile_name" id="0x7f100038" />
    <public type="string" name="dialog_btn_OK" id="0x7f100039" />
    <public type="string" name="dialog_btn_cancel" id="0x7f10003a" />
    <public type="string" name="fab_transformation_scrim_behavior" id="0x7f10003b" />
    <public type="string" name="fab_transformation_sheet_behavior" id="0x7f10003c" />
    <public type="string" name="fastscroll__app_name" id="0x7f10003d" />
    <public type="string" name="function_introduction" id="0x7f10003e" />
    <public type="string" name="fw_config_active" id="0x7f10003f" />
    <public type="string" name="fw_config_inactive" id="0x7f100040" />
    <public type="string" name="fw_dump_ping" id="0x7f100041" />
    <public type="string" name="fw_dump_ping_off" id="0x7f100042" />
    <public type="string" name="fw_dump_ping_on" id="0x7f100043" />
    <public type="string" name="fw_info_bandwidth" id="0x7f100044" />
    <public type="string" name="fw_info_bias" id="0x7f100045" />
    <public type="string" name="fw_info_delay" id="0x7f100046" />
    <public type="string" name="fw_info_in_pass_loss" id="0x7f100047" />
    <public type="string" name="fw_info_no_config" id="0x7f100048" />
    <public type="string" name="fw_info_out_pass_loss" id="0x7f100049" />
    <public type="string" name="fw_info_ping" id="0x7f10004a" />
    <public type="string" name="fw_info_profile_active" id="0x7f10004b" />
    <public type="string" name="fw_info_profile_inactive" id="0x7f10004c" />
    <public type="string" name="fw_info_protocol" id="0x7f10004d" />
    <public type="string" name="fw_info_rate" id="0x7f10004e" />
    <public type="string" name="fw_msg_ask_for_permission" id="0x7f10004f" />
    <public type="string" name="fw_msg_ask_for_permission_below_23" id="0x7f100050" />
    <public type="string" name="fw_process_switch" id="0x7f100051" />
    <public type="string" name="help_description" id="0x7f100052" />
    <public type="string" name="hide_bottom_view_on_scroll_behavior" id="0x7f100053" />
    <public type="string" name="httpTimeOutThread" id="0x7f100054" />
    <public type="string" name="lable_click_select_progress" id="0x7f100055" />
    <public type="string" name="lable_select_progress" id="0x7f100056" />
    <public type="string" name="log_network_updated" id="0x7f100057" />
    <public type="string" name="log_test_program" id="0x7f100058" />
    <public type="string" name="login_qq" id="0x7f100059" />
    <public type="string" name="login_rtx" id="0x7f10005a" />
    <public type="string" name="menu_about" id="0x7f10005b" />
    <public type="string" name="menu_logout" id="0x7f10005c" />
    <public type="string" name="menu_logout_tips" id="0x7f10005d" />
    <public type="string" name="menu_quit" id="0x7f10005e" />
    <public type="string" name="menu_settings" id="0x7f10005f" />
    <public type="string" name="msg_about" id="0x7f100060" />
    <public type="string" name="msg_cannot_delete_profile" id="0x7f100061" />
    <public type="string" name="msg_save_profile" id="0x7f100062" />
    <public type="string" name="msg_select_profile" id="0x7f100063" />
    <public type="string" name="msg_select_progress" id="0x7f100064" />
    <public type="string" name="msg_vpn_is_running" id="0x7f100065" />
    <public type="string" name="mtrl_chip_close_icon_content_description" id="0x7f100066" />
    <public type="string" name="net_down_con_drop" id="0x7f100067" />
    <public type="string" name="net_down_delay" id="0x7f100068" />
    <public type="string" name="net_down_ran_drop" id="0x7f100069" />
    <public type="string" name="net_head_con_drop" id="0x7f10006a" />
    <public type="string" name="net_head_delay" id="0x7f10006b" />
    <public type="string" name="net_head_protocal" id="0x7f10006c" />
    <public type="string" name="net_head_ran_drop" id="0x7f10006d" />
    <public type="string" name="net_key_down_condrop" id="0x7f10006e" />
    <public type="string" name="net_key_down_delay" id="0x7f10006f" />
    <public type="string" name="net_key_down_randrop" id="0x7f100070" />
    <public type="string" name="net_key_protocol" id="0x7f100071" />
    <public type="string" name="net_key_up_condrop" id="0x7f100072" />
    <public type="string" name="net_key_up_delay" id="0x7f100073" />
    <public type="string" name="net_key_up_randrop" id="0x7f100074" />
    <public type="string" name="net_protocal_both" id="0x7f100075" />
    <public type="string" name="net_protocal_tcp" id="0x7f100076" />
    <public type="string" name="net_protocal_udp" id="0x7f100077" />
    <public type="string" name="net_summary_condrop_format" id="0x7f100078" />
    <public type="string" name="net_summary_condrop_nosetting" id="0x7f100079" />
    <public type="string" name="net_title_InBandwidth" id="0x7f10007a" />
    <public type="string" name="net_title_InDelay" id="0x7f10007b" />
    <public type="string" name="net_title_InDelayBias" id="0x7f10007c" />
    <public type="string" name="net_title_InLoss" id="0x7f10007d" />
    <public type="string" name="net_title_InPass" id="0x7f10007e" />
    <public type="string" name="net_title_InRate" id="0x7f10007f" />
    <public type="string" name="net_title_OutBandwidth" id="0x7f100080" />
    <public type="string" name="net_title_OutDelay" id="0x7f100081" />
    <public type="string" name="net_title_OutDelayBias" id="0x7f100082" />
    <public type="string" name="net_title_OutLoss" id="0x7f100083" />
    <public type="string" name="net_title_OutPass" id="0x7f100084" />
    <public type="string" name="net_title_OutRate" id="0x7f100085" />
    <public type="string" name="net_title_Protocol" id="0x7f100086" />
    <public type="string" name="net_title_condrop_drop" id="0x7f100087" />
    <public type="string" name="net_title_condrop_pass" id="0x7f100088" />
    <public type="string" name="net_up_con_drop" id="0x7f100089" />
    <public type="string" name="net_up_delay" id="0x7f10008a" />
    <public type="string" name="net_up_ran_drop" id="0x7f10008b" />
    <public type="string" name="password_toggle_content_description" id="0x7f10008c" />
    <public type="string" name="path_password_eye" id="0x7f10008d" />
    <public type="string" name="path_password_eye_mask_strike_through" id="0x7f10008e" />
    <public type="string" name="path_password_eye_mask_visible" id="0x7f10008f" />
    <public type="string" name="path_password_strike_through" id="0x7f100090" />
    <public type="string" name="profileDownLoadHttpErrorTips" id="0x7f100091" />
    <public type="string" name="profileDownLoadTimeOutTips" id="0x7f100092" />
    <public type="string" name="profileDownLoadTitle" id="0x7f100093" />
    <public type="string" name="profile_edit_tips" id="0x7f100094" />
    <public type="string" name="profile_preset" id="0x7f100095" />
    <public type="string" name="search_menu_title" id="0x7f100096" />
    <public type="string" name="setting_header_about" id="0x7f100097" />
    <public type="string" name="setting_header_general" id="0x7f100098" />
    <public type="string" name="setting_key_dump_pcap" id="0x7f100099" />
    <public type="string" name="setting_key_fw_ctrl" id="0x7f10009a" />
    <public type="string" name="setting_key_fw_info" id="0x7f10009b" />
    <public type="string" name="setting_key_release_note" id="0x7f10009c" />
    <public type="string" name="setting_key_send_feedback" id="0x7f10009d" />
    <public type="string" name="setting_key_update" id="0x7f10009e" />
    <public type="string" name="setting_key_version" id="0x7f10009f" />
    <public type="string" name="setting_summary_about" id="0x7f1000a0" />
    <public type="string" name="setting_summary_dump_pcap" id="0x7f1000a1" />
    <public type="string" name="setting_summary_fw_ctrl" id="0x7f1000a2" />
    <public type="string" name="setting_summary_fw_info" id="0x7f1000a3" />
    <public type="string" name="setting_summary_send_feedback" id="0x7f1000a4" />
    <public type="string" name="setting_summary_update" id="0x7f1000a5" />
    <public type="string" name="setting_summary_version" id="0x7f1000a6" />
    <public type="string" name="setting_title_dump_pcap" id="0x7f1000a7" />
    <public type="string" name="setting_title_fw_ctrl" id="0x7f1000a8" />
    <public type="string" name="setting_title_fw_info" id="0x7f1000a9" />
    <public type="string" name="setting_title_send_feedback" id="0x7f1000ab" />
    <public type="string" name="setting_title_update" id="0x7f1000ac" />
    <public type="string" name="setting_title_version" id="0x7f1000ad" />
    <public type="string" name="status_bar_notification_info_overflow" id="0x7f1000ae" />
    <public type="string" name="tab_head_config" id="0x7f1000af" />
    <public type="string" name="tab_head_log" id="0x7f1000b0" />
    <public type="string" name="title_Login" id="0x7f1000b1" />
    <public type="string" name="title_activity_settings" id="0x7f1000b2" />
    <public type="string" name="title_create_new_config" id="0x7f1000b3" />
    <public type="string" name="title_profile_name" id="0x7f1000b4" />
    <public type="string" name="toast_net_config_activated" id="0x7f1000b5" />
    <public type="string" name="tv_login_loading" id="0x7f1000b6" />
    <public type="string" name="user_custom_desc" id="0x7f1000b7" />
    <public type="string" name="versionUpdateHttpErrorTips" id="0x7f1000b9" />
    <public type="string" name="versionUpdateTimeOutTips" id="0x7f1000ba" />
    <public type="string" name="versionUpdateTips" id="0x7f1000bb" />
    <public type="string" name="versionUpdateTitle" id="0x7f1000bc" />
    <public type="string" name="vpn_connected_status" id="0x7f1000bd" />
    <public type="string" name="vpn_disconnected_status" id="0x7f1000be" />
    <public type="string" name="what_is_new" id="0x7f1000bf" />
    <public type="style" name="AlertDialog.AppCompat" id="0x7f110000" />
    <public type="style" name="AlertDialog.AppCompat.Light" id="0x7f110001" />
    <public type="style" name="Animation.AppCompat.Dialog" id="0x7f110002" />
    <public type="style" name="Animation.AppCompat.DropDownUp" id="0x7f110003" />
    <public type="style" name="Animation.AppCompat.Tooltip" id="0x7f110004" />
    <public type="style" name="Animation.Design.BottomSheetDialog" id="0x7f110005" />
    <public type="style" name="AppTheme" id="0x7f110006" />
    <public type="style" name="AppTheme.AppBarOverlay" id="0x7f110007" />
    <public type="style" name="AppTheme.NoActionBar" id="0x7f110008" />
    <public type="style" name="AppTheme.PopupOverlay" id="0x7f110009" />
    <public type="style" name="Base.AlertDialog.AppCompat" id="0x7f11000a" />
    <public type="style" name="Base.AlertDialog.AppCompat.Light" id="0x7f11000b" />
    <public type="style" name="Base.Animation.AppCompat.Dialog" id="0x7f11000c" />
    <public type="style" name="Base.Animation.AppCompat.DropDownUp" id="0x7f11000d" />
    <public type="style" name="Base.Animation.AppCompat.Tooltip" id="0x7f11000e" />
    <public type="style" name="Base.CardView" id="0x7f11000f" />
    <public type="style" name="Base.DialogWindowTitle.AppCompat" id="0x7f110010" />
    <public type="style" name="Base.DialogWindowTitleBackground.AppCompat" id="0x7f110011" />
    <public type="style" name="Base.TextAppearance.AppCompat" id="0x7f110012" />
    <public type="style" name="Base.TextAppearance.AppCompat.Body1" id="0x7f110013" />
    <public type="style" name="Base.TextAppearance.AppCompat.Body2" id="0x7f110014" />
    <public type="style" name="Base.TextAppearance.AppCompat.Button" id="0x7f110015" />
    <public type="style" name="Base.TextAppearance.AppCompat.Caption" id="0x7f110016" />
    <public type="style" name="Base.TextAppearance.AppCompat.Display1" id="0x7f110017" />
    <public type="style" name="Base.TextAppearance.AppCompat.Display2" id="0x7f110018" />
    <public type="style" name="Base.TextAppearance.AppCompat.Display3" id="0x7f110019" />
    <public type="style" name="Base.TextAppearance.AppCompat.Display4" id="0x7f11001a" />
    <public type="style" name="Base.TextAppearance.AppCompat.Headline" id="0x7f11001b" />
    <public type="style" name="Base.TextAppearance.AppCompat.Inverse" id="0x7f11001c" />
    <public type="style" name="Base.TextAppearance.AppCompat.Large" id="0x7f11001d" />
    <public type="style" name="Base.TextAppearance.AppCompat.Large.Inverse" id="0x7f11001e" />
    <public type="style" name="Base.TextAppearance.AppCompat.Light.Widget.PopupMenu.Large" id="0x7f11001f" />
    <public type="style" name="Base.TextAppearance.AppCompat.Light.Widget.PopupMenu.Small" id="0x7f110020" />
    <public type="style" name="Base.TextAppearance.AppCompat.Medium" id="0x7f110021" />
    <public type="style" name="Base.TextAppearance.AppCompat.Medium.Inverse" id="0x7f110022" />
    <public type="style" name="Base.TextAppearance.AppCompat.Menu" id="0x7f110023" />
    <public type="style" name="Base.TextAppearance.AppCompat.SearchResult" id="0x7f110024" />
    <public type="style" name="Base.TextAppearance.AppCompat.SearchResult.Subtitle" id="0x7f110025" />
    <public type="style" name="Base.TextAppearance.AppCompat.SearchResult.Title" id="0x7f110026" />
    <public type="style" name="Base.TextAppearance.AppCompat.Small" id="0x7f110027" />
    <public type="style" name="Base.TextAppearance.AppCompat.Small.Inverse" id="0x7f110028" />
    <public type="style" name="Base.TextAppearance.AppCompat.Subhead" id="0x7f110029" />
    <public type="style" name="Base.TextAppearance.AppCompat.Subhead.Inverse" id="0x7f11002a" />
    <public type="style" name="Base.TextAppearance.AppCompat.Title" id="0x7f11002b" />
    <public type="style" name="Base.TextAppearance.AppCompat.Title.Inverse" id="0x7f11002c" />
    <public type="style" name="Base.TextAppearance.AppCompat.Tooltip" id="0x7f11002d" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.ActionBar.Menu" id="0x7f11002e" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle" id="0x7f11002f" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse" id="0x7f110030" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.ActionBar.Title" id="0x7f110031" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse" id="0x7f110032" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.ActionMode.Subtitle" id="0x7f110033" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.ActionMode.Title" id="0x7f110034" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.Button" id="0x7f110035" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.Button.Borderless.Colored" id="0x7f110036" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.Button.Colored" id="0x7f110037" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.Button.Inverse" id="0x7f110038" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.DropDownItem" id="0x7f110039" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.PopupMenu.Header" id="0x7f11003a" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.PopupMenu.Large" id="0x7f11003b" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.PopupMenu.Small" id="0x7f11003c" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.Switch" id="0x7f11003d" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.TextView.SpinnerItem" id="0x7f11003e" />
    <public type="style" name="Base.TextAppearance.Widget.AppCompat.ExpandedMenu.Item" id="0x7f11003f" />
    <public type="style" name="Base.TextAppearance.Widget.AppCompat.Toolbar.Subtitle" id="0x7f110040" />
    <public type="style" name="Base.TextAppearance.Widget.AppCompat.Toolbar.Title" id="0x7f110041" />
    <public type="style" name="Base.Theme.AppCompat" id="0x7f110042" />
    <public type="style" name="Base.Theme.AppCompat.CompactMenu" id="0x7f110043" />
    <public type="style" name="Base.Theme.AppCompat.Dialog" id="0x7f110044" />
    <public type="style" name="Base.Theme.AppCompat.Dialog.Alert" id="0x7f110045" />
    <public type="style" name="Base.Theme.AppCompat.Dialog.FixedSize" id="0x7f110046" />
    <public type="style" name="Base.Theme.AppCompat.Dialog.MinWidth" id="0x7f110047" />
    <public type="style" name="Base.Theme.AppCompat.DialogWhenLarge" id="0x7f110048" />
    <public type="style" name="Base.Theme.AppCompat.Light" id="0x7f110049" />
    <public type="style" name="Base.Theme.AppCompat.Light.DarkActionBar" id="0x7f11004a" />
    <public type="style" name="Base.Theme.AppCompat.Light.Dialog" id="0x7f11004b" />
    <public type="style" name="Base.Theme.AppCompat.Light.Dialog.Alert" id="0x7f11004c" />
    <public type="style" name="Base.Theme.AppCompat.Light.Dialog.FixedSize" id="0x7f11004d" />
    <public type="style" name="Base.Theme.AppCompat.Light.Dialog.MinWidth" id="0x7f11004e" />
    <public type="style" name="Base.Theme.AppCompat.Light.DialogWhenLarge" id="0x7f11004f" />
    <public type="style" name="Base.Theme.MaterialComponents" id="0x7f110050" />
    <public type="style" name="Base.Theme.MaterialComponents.Bridge" id="0x7f110051" />
    <public type="style" name="Base.Theme.MaterialComponents.CompactMenu" id="0x7f110052" />
    <public type="style" name="Base.Theme.MaterialComponents.Dialog" id="0x7f110053" />
    <public type="style" name="Base.Theme.MaterialComponents.Dialog.Alert" id="0x7f110054" />
    <public type="style" name="Base.Theme.MaterialComponents.Dialog.FixedSize" id="0x7f110055" />
    <public type="style" name="Base.Theme.MaterialComponents.Dialog.MinWidth" id="0x7f110056" />
    <public type="style" name="Base.Theme.MaterialComponents.DialogWhenLarge" id="0x7f110057" />
    <public type="style" name="Base.Theme.MaterialComponents.Light" id="0x7f110058" />
    <public type="style" name="Base.Theme.MaterialComponents.Light.Bridge" id="0x7f110059" />
    <public type="style" name="Base.Theme.MaterialComponents.Light.DarkActionBar" id="0x7f11005a" />
    <public type="style" name="Base.Theme.MaterialComponents.Light.DarkActionBar.Bridge" id="0x7f11005b" />
    <public type="style" name="Base.Theme.MaterialComponents.Light.Dialog" id="0x7f11005c" />
    <public type="style" name="Base.Theme.MaterialComponents.Light.Dialog.Alert" id="0x7f11005d" />
    <public type="style" name="Base.Theme.MaterialComponents.Light.Dialog.FixedSize" id="0x7f11005e" />
    <public type="style" name="Base.Theme.MaterialComponents.Light.Dialog.MinWidth" id="0x7f11005f" />
    <public type="style" name="Base.Theme.MaterialComponents.Light.DialogWhenLarge" id="0x7f110060" />
    <public type="style" name="Base.ThemeOverlay.AppCompat" id="0x7f110061" />
    <public type="style" name="Base.ThemeOverlay.AppCompat.ActionBar" id="0x7f110062" />
    <public type="style" name="Base.ThemeOverlay.AppCompat.Dark" id="0x7f110063" />
    <public type="style" name="Base.ThemeOverlay.AppCompat.Dark.ActionBar" id="0x7f110064" />
    <public type="style" name="Base.ThemeOverlay.AppCompat.Dialog" id="0x7f110065" />
    <public type="style" name="Base.ThemeOverlay.AppCompat.Dialog.Alert" id="0x7f110066" />
    <public type="style" name="Base.ThemeOverlay.AppCompat.Light" id="0x7f110067" />
    <public type="style" name="Base.ThemeOverlay.MaterialComponents.Dialog" id="0x7f110068" />
    <public type="style" name="Base.ThemeOverlay.MaterialComponents.Dialog.Alert" id="0x7f110069" />
    <public type="style" name="Base.V14.Theme.MaterialComponents" id="0x7f11006a" />
    <public type="style" name="Base.V14.Theme.MaterialComponents.Bridge" id="0x7f11006b" />
    <public type="style" name="Base.V14.Theme.MaterialComponents.Dialog" id="0x7f11006c" />
    <public type="style" name="Base.V14.Theme.MaterialComponents.Light" id="0x7f11006d" />
    <public type="style" name="Base.V14.Theme.MaterialComponents.Light.Bridge" id="0x7f11006e" />
    <public type="style" name="Base.V14.Theme.MaterialComponents.Light.DarkActionBar.Bridge" id="0x7f11006f" />
    <public type="style" name="Base.V14.Theme.MaterialComponents.Light.Dialog" id="0x7f110070" />
    <public type="style" name="Base.V14.ThemeOverlay.MaterialComponents.Dialog" id="0x7f110071" />
    <public type="style" name="Base.V14.ThemeOverlay.MaterialComponents.Dialog.Alert" id="0x7f110072" />
    <public type="style" name="Base.V21.Theme.AppCompat" id="0x7f110073" />
    <public type="style" name="Base.V21.Theme.AppCompat.Dialog" id="0x7f110074" />
    <public type="style" name="Base.V21.Theme.AppCompat.Light" id="0x7f110075" />
    <public type="style" name="Base.V21.Theme.AppCompat.Light.Dialog" id="0x7f110076" />
    <public type="style" name="Base.V21.ThemeOverlay.AppCompat.Dialog" id="0x7f110077" />
    <public type="style" name="Base.V7.Theme.AppCompat" id="0x7f110081" />
    <public type="style" name="Base.V7.Theme.AppCompat.Dialog" id="0x7f110082" />
    <public type="style" name="Base.V7.Theme.AppCompat.Light" id="0x7f110083" />
    <public type="style" name="Base.V7.Theme.AppCompat.Light.Dialog" id="0x7f110084" />
    <public type="style" name="Base.V7.ThemeOverlay.AppCompat.Dialog" id="0x7f110085" />
    <public type="style" name="Base.V7.Widget.AppCompat.AutoCompleteTextView" id="0x7f110086" />
    <public type="style" name="Base.V7.Widget.AppCompat.EditText" id="0x7f110087" />
    <public type="style" name="Base.V7.Widget.AppCompat.Toolbar" id="0x7f110088" />
    <public type="style" name="Base.Widget.AppCompat.ActionBar" id="0x7f110089" />
    <public type="style" name="Base.Widget.AppCompat.ActionBar.Solid" id="0x7f11008a" />
    <public type="style" name="Base.Widget.AppCompat.ActionBar.TabBar" id="0x7f11008b" />
    <public type="style" name="Base.Widget.AppCompat.ActionBar.TabText" id="0x7f11008c" />
    <public type="style" name="Base.Widget.AppCompat.ActionBar.TabView" id="0x7f11008d" />
    <public type="style" name="Base.Widget.AppCompat.ActionButton" id="0x7f11008e" />
    <public type="style" name="Base.Widget.AppCompat.ActionButton.CloseMode" id="0x7f11008f" />
    <public type="style" name="Base.Widget.AppCompat.ActionButton.Overflow" id="0x7f110090" />
    <public type="style" name="Base.Widget.AppCompat.ActionMode" id="0x7f110091" />
    <public type="style" name="Base.Widget.AppCompat.ActivityChooserView" id="0x7f110092" />
    <public type="style" name="Base.Widget.AppCompat.AutoCompleteTextView" id="0x7f110093" />
    <public type="style" name="Base.Widget.AppCompat.Button" id="0x7f110094" />
    <public type="style" name="Base.Widget.AppCompat.Button.Borderless" id="0x7f110095" />
    <public type="style" name="Base.Widget.AppCompat.Button.Borderless.Colored" id="0x7f110096" />
    <public type="style" name="Base.Widget.AppCompat.Button.ButtonBar.AlertDialog" id="0x7f110097" />
    <public type="style" name="Base.Widget.AppCompat.Button.Colored" id="0x7f110098" />
    <public type="style" name="Base.Widget.AppCompat.Button.Small" id="0x7f110099" />
    <public type="style" name="Base.Widget.AppCompat.ButtonBar" id="0x7f11009a" />
    <public type="style" name="Base.Widget.AppCompat.ButtonBar.AlertDialog" id="0x7f11009b" />
    <public type="style" name="Base.Widget.AppCompat.CompoundButton.CheckBox" id="0x7f11009c" />
    <public type="style" name="Base.Widget.AppCompat.CompoundButton.RadioButton" id="0x7f11009d" />
    <public type="style" name="Base.Widget.AppCompat.CompoundButton.Switch" id="0x7f11009e" />
    <public type="style" name="Base.Widget.AppCompat.DrawerArrowToggle" id="0x7f11009f" />
    <public type="style" name="Base.Widget.AppCompat.DrawerArrowToggle.Common" id="0x7f1100a0" />
    <public type="style" name="Base.Widget.AppCompat.DropDownItem.Spinner" id="0x7f1100a1" />
    <public type="style" name="Base.Widget.AppCompat.EditText" id="0x7f1100a2" />
    <public type="style" name="Base.Widget.AppCompat.ImageButton" id="0x7f1100a3" />
    <public type="style" name="Base.Widget.AppCompat.Light.ActionBar" id="0x7f1100a4" />
    <public type="style" name="Base.Widget.AppCompat.Light.ActionBar.Solid" id="0x7f1100a5" />
    <public type="style" name="Base.Widget.AppCompat.Light.ActionBar.TabBar" id="0x7f1100a6" />
    <public type="style" name="Base.Widget.AppCompat.Light.ActionBar.TabText" id="0x7f1100a7" />
    <public type="style" name="Base.Widget.AppCompat.Light.ActionBar.TabText.Inverse" id="0x7f1100a8" />
    <public type="style" name="Base.Widget.AppCompat.Light.ActionBar.TabView" id="0x7f1100a9" />
    <public type="style" name="Base.Widget.AppCompat.Light.PopupMenu" id="0x7f1100aa" />
    <public type="style" name="Base.Widget.AppCompat.Light.PopupMenu.Overflow" id="0x7f1100ab" />
    <public type="style" name="Base.Widget.AppCompat.ListMenuView" id="0x7f1100ac" />
    <public type="style" name="Base.Widget.AppCompat.ListPopupWindow" id="0x7f1100ad" />
    <public type="style" name="Base.Widget.AppCompat.ListView" id="0x7f1100ae" />
    <public type="style" name="Base.Widget.AppCompat.ListView.DropDown" id="0x7f1100af" />
    <public type="style" name="Base.Widget.AppCompat.ListView.Menu" id="0x7f1100b0" />
    <public type="style" name="Base.Widget.AppCompat.PopupMenu" id="0x7f1100b1" />
    <public type="style" name="Base.Widget.AppCompat.PopupMenu.Overflow" id="0x7f1100b2" />
    <public type="style" name="Base.Widget.AppCompat.PopupWindow" id="0x7f1100b3" />
    <public type="style" name="Base.Widget.AppCompat.ProgressBar" id="0x7f1100b4" />
    <public type="style" name="Base.Widget.AppCompat.ProgressBar.Horizontal" id="0x7f1100b5" />
    <public type="style" name="Base.Widget.AppCompat.RatingBar" id="0x7f1100b6" />
    <public type="style" name="Base.Widget.AppCompat.RatingBar.Indicator" id="0x7f1100b7" />
    <public type="style" name="Base.Widget.AppCompat.RatingBar.Small" id="0x7f1100b8" />
    <public type="style" name="Base.Widget.AppCompat.SearchView" id="0x7f1100b9" />
    <public type="style" name="Base.Widget.AppCompat.SearchView.ActionBar" id="0x7f1100ba" />
    <public type="style" name="Base.Widget.AppCompat.SeekBar" id="0x7f1100bb" />
    <public type="style" name="Base.Widget.AppCompat.SeekBar.Discrete" id="0x7f1100bc" />
    <public type="style" name="Base.Widget.AppCompat.Spinner" id="0x7f1100bd" />
    <public type="style" name="Base.Widget.AppCompat.Spinner.Underlined" id="0x7f1100be" />
    <public type="style" name="Base.Widget.AppCompat.TextView.SpinnerItem" id="0x7f1100bf" />
    <public type="style" name="Base.Widget.AppCompat.Toolbar" id="0x7f1100c0" />
    <public type="style" name="Base.Widget.AppCompat.Toolbar.Button.Navigation" id="0x7f1100c1" />
    <public type="style" name="Base.Widget.Design.TabLayout" id="0x7f1100c2" />
    <public type="style" name="Base.Widget.MaterialComponents.Chip" id="0x7f1100c3" />
    <public type="style" name="Base.Widget.MaterialComponents.TextInputEditText" id="0x7f1100c4" />
    <public type="style" name="Base.Widget.MaterialComponents.TextInputLayout" id="0x7f1100c5" />
    <public type="style" name="CardView" id="0x7f1100c6" />
    <public type="style" name="CardView.Dark" id="0x7f1100c7" />
    <public type="style" name="CardView.Light" id="0x7f1100c8" />
    <public type="style" name="Platform.AppCompat" id="0x7f1100c9" />
    <public type="style" name="Platform.AppCompat.Light" id="0x7f1100ca" />
    <public type="style" name="Platform.MaterialComponents" id="0x7f1100cb" />
    <public type="style" name="Platform.MaterialComponents.Dialog" id="0x7f1100cc" />
    <public type="style" name="Platform.MaterialComponents.Light" id="0x7f1100cd" />
    <public type="style" name="Platform.MaterialComponents.Light.Dialog" id="0x7f1100ce" />
    <public type="style" name="Platform.ThemeOverlay.AppCompat" id="0x7f1100cf" />
    <public type="style" name="Platform.ThemeOverlay.AppCompat.Dark" id="0x7f1100d0" />
    <public type="style" name="Platform.ThemeOverlay.AppCompat.Light" id="0x7f1100d1" />
    <public type="style" name="Platform.V21.AppCompat" id="0x7f1100d2" />
    <public type="style" name="Platform.V21.AppCompat.Light" id="0x7f1100d3" />
    <public type="style" name="Platform.Widget.AppCompat.Spinner" id="0x7f1100d6" />
    <public type="style" name="RtlOverlay.DialogWindowTitle.AppCompat" id="0x7f1100d7" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.ActionBar.TitleItem" id="0x7f1100d8" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.DialogTitle.Icon" id="0x7f1100d9" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.PopupMenuItem" id="0x7f1100da" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.PopupMenuItem.InternalGroup" id="0x7f1100db" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.PopupMenuItem.Shortcut" id="0x7f1100dc" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.PopupMenuItem.SubmenuArrow" id="0x7f1100dd" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.PopupMenuItem.Text" id="0x7f1100de" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.PopupMenuItem.Title" id="0x7f1100df" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.Search.DropDown" id="0x7f1100e0" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.Search.DropDown.Icon1" id="0x7f1100e1" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.Search.DropDown.Icon2" id="0x7f1100e2" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.Search.DropDown.Query" id="0x7f1100e3" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.Search.DropDown.Text" id="0x7f1100e4" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.SearchView.MagIcon" id="0x7f1100e5" />
    <public type="style" name="RtlUnderlay.Widget.AppCompat.ActionButton" id="0x7f1100e6" />
    <public type="style" name="RtlUnderlay.Widget.AppCompat.ActionButton.Overflow" id="0x7f1100e7" />
    <public type="style" name="StyledScrollerTextAppearance" id="0x7f1100e8" />
    <public type="style" name="TextAppearance.AppCompat" id="0x7f1100e9" />
    <public type="style" name="TextAppearance.AppCompat.Body1" id="0x7f1100ea" />
    <public type="style" name="TextAppearance.AppCompat.Body2" id="0x7f1100eb" />
    <public type="style" name="TextAppearance.AppCompat.Button" id="0x7f1100ec" />
    <public type="style" name="TextAppearance.AppCompat.Caption" id="0x7f1100ed" />
    <public type="style" name="TextAppearance.AppCompat.Display1" id="0x7f1100ee" />
    <public type="style" name="TextAppearance.AppCompat.Display2" id="0x7f1100ef" />
    <public type="style" name="TextAppearance.AppCompat.Display3" id="0x7f1100f0" />
    <public type="style" name="TextAppearance.AppCompat.Display4" id="0x7f1100f1" />
    <public type="style" name="TextAppearance.AppCompat.Headline" id="0x7f1100f2" />
    <public type="style" name="TextAppearance.AppCompat.Inverse" id="0x7f1100f3" />
    <public type="style" name="TextAppearance.AppCompat.Large" id="0x7f1100f4" />
    <public type="style" name="TextAppearance.AppCompat.Large.Inverse" id="0x7f1100f5" />
    <public type="style" name="TextAppearance.AppCompat.Light.SearchResult.Subtitle" id="0x7f1100f6" />
    <public type="style" name="TextAppearance.AppCompat.Light.SearchResult.Title" id="0x7f1100f7" />
    <public type="style" name="TextAppearance.AppCompat.Light.Widget.PopupMenu.Large" id="0x7f1100f8" />
    <public type="style" name="TextAppearance.AppCompat.Light.Widget.PopupMenu.Small" id="0x7f1100f9" />
    <public type="style" name="TextAppearance.AppCompat.Medium" id="0x7f1100fa" />
    <public type="style" name="TextAppearance.AppCompat.Medium.Inverse" id="0x7f1100fb" />
    <public type="style" name="TextAppearance.AppCompat.Menu" id="0x7f1100fc" />
    <public type="style" name="TextAppearance.AppCompat.SearchResult.Subtitle" id="0x7f1100fd" />
    <public type="style" name="TextAppearance.AppCompat.SearchResult.Title" id="0x7f1100fe" />
    <public type="style" name="TextAppearance.AppCompat.Small" id="0x7f1100ff" />
    <public type="style" name="TextAppearance.AppCompat.Small.Inverse" id="0x7f110100" />
    <public type="style" name="TextAppearance.AppCompat.Subhead" id="0x7f110101" />
    <public type="style" name="TextAppearance.AppCompat.Subhead.Inverse" id="0x7f110102" />
    <public type="style" name="TextAppearance.AppCompat.Title" id="0x7f110103" />
    <public type="style" name="TextAppearance.AppCompat.Title.Inverse" id="0x7f110104" />
    <public type="style" name="TextAppearance.AppCompat.Tooltip" id="0x7f110105" />
    <public type="style" name="TextAppearance.AppCompat.Widget.ActionBar.Menu" id="0x7f110106" />
    <public type="style" name="TextAppearance.AppCompat.Widget.ActionBar.Subtitle" id="0x7f110107" />
    <public type="style" name="TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse" id="0x7f110108" />
    <public type="style" name="TextAppearance.AppCompat.Widget.ActionBar.Title" id="0x7f110109" />
    <public type="style" name="TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse" id="0x7f11010a" />
    <public type="style" name="TextAppearance.AppCompat.Widget.ActionMode.Subtitle" id="0x7f11010b" />
    <public type="style" name="TextAppearance.AppCompat.Widget.ActionMode.Subtitle.Inverse" id="0x7f11010c" />
    <public type="style" name="TextAppearance.AppCompat.Widget.ActionMode.Title" id="0x7f11010d" />
    <public type="style" name="TextAppearance.AppCompat.Widget.ActionMode.Title.Inverse" id="0x7f11010e" />
    <public type="style" name="TextAppearance.AppCompat.Widget.Button" id="0x7f11010f" />
    <public type="style" name="TextAppearance.AppCompat.Widget.Button.Borderless.Colored" id="0x7f110110" />
    <public type="style" name="TextAppearance.AppCompat.Widget.Button.Colored" id="0x7f110111" />
    <public type="style" name="TextAppearance.AppCompat.Widget.Button.Inverse" id="0x7f110112" />
    <public type="style" name="TextAppearance.AppCompat.Widget.DropDownItem" id="0x7f110113" />
    <public type="style" name="TextAppearance.AppCompat.Widget.PopupMenu.Header" id="0x7f110114" />
    <public type="style" name="TextAppearance.AppCompat.Widget.PopupMenu.Large" id="0x7f110115" />
    <public type="style" name="TextAppearance.AppCompat.Widget.PopupMenu.Small" id="0x7f110116" />
    <public type="style" name="TextAppearance.AppCompat.Widget.Switch" id="0x7f110117" />
    <public type="style" name="TextAppearance.AppCompat.Widget.TextView.SpinnerItem" id="0x7f110118" />
    <public type="style" name="TextAppearance.Compat.Notification" id="0x7f110119" />
    <public type="style" name="TextAppearance.Compat.Notification.Info" id="0x7f11011a" />
    <public type="style" name="TextAppearance.Compat.Notification.Info.Media" id="0x7f11011b" />
    <public type="style" name="TextAppearance.Compat.Notification.Line2" id="0x7f11011c" />
    <public type="style" name="TextAppearance.Compat.Notification.Line2.Media" id="0x7f11011d" />
    <public type="style" name="TextAppearance.Compat.Notification.Media" id="0x7f11011e" />
    <public type="style" name="TextAppearance.Compat.Notification.Time" id="0x7f11011f" />
    <public type="style" name="TextAppearance.Compat.Notification.Time.Media" id="0x7f110120" />
    <public type="style" name="TextAppearance.Compat.Notification.Title" id="0x7f110121" />
    <public type="style" name="TextAppearance.Compat.Notification.Title.Media" id="0x7f110122" />
    <public type="style" name="TextAppearance.Design.CollapsingToolbar.Expanded" id="0x7f110123" />
    <public type="style" name="TextAppearance.Design.Counter" id="0x7f110124" />
    <public type="style" name="TextAppearance.Design.Counter.Overflow" id="0x7f110125" />
    <public type="style" name="TextAppearance.Design.Error" id="0x7f110126" />
    <public type="style" name="TextAppearance.Design.HelperText" id="0x7f110127" />
    <public type="style" name="TextAppearance.Design.Hint" id="0x7f110128" />
    <public type="style" name="TextAppearance.Design.Snackbar.Message" id="0x7f110129" />
    <public type="style" name="TextAppearance.Design.Tab" id="0x7f11012a" />
    <public type="style" name="TextAppearance.MaterialComponents.Body1" id="0x7f11012b" />
    <public type="style" name="TextAppearance.MaterialComponents.Body2" id="0x7f11012c" />
    <public type="style" name="TextAppearance.MaterialComponents.Button" id="0x7f11012d" />
    <public type="style" name="TextAppearance.MaterialComponents.Caption" id="0x7f11012e" />
    <public type="style" name="TextAppearance.MaterialComponents.Chip" id="0x7f11012f" />
    <public type="style" name="TextAppearance.MaterialComponents.Headline1" id="0x7f110130" />
    <public type="style" name="TextAppearance.MaterialComponents.Headline2" id="0x7f110131" />
    <public type="style" name="TextAppearance.MaterialComponents.Headline3" id="0x7f110132" />
    <public type="style" name="TextAppearance.MaterialComponents.Headline4" id="0x7f110133" />
    <public type="style" name="TextAppearance.MaterialComponents.Headline5" id="0x7f110134" />
    <public type="style" name="TextAppearance.MaterialComponents.Headline6" id="0x7f110135" />
    <public type="style" name="TextAppearance.MaterialComponents.Overline" id="0x7f110136" />
    <public type="style" name="TextAppearance.MaterialComponents.Subtitle1" id="0x7f110137" />
    <public type="style" name="TextAppearance.MaterialComponents.Subtitle2" id="0x7f110138" />
    <public type="style" name="TextAppearance.MaterialComponents.Tab" id="0x7f110139" />
    <public type="style" name="TextAppearance.Widget.AppCompat.ExpandedMenu.Item" id="0x7f11013a" />
    <public type="style" name="TextAppearance.Widget.AppCompat.Toolbar.Subtitle" id="0x7f11013b" />
    <public type="style" name="TextAppearance.Widget.AppCompat.Toolbar.Title" id="0x7f11013c" />
    <public type="style" name="Theme.AppCompat" id="0x7f11013d" />
    <public type="style" name="Theme.AppCompat.CompactMenu" id="0x7f11013e" />
    <public type="style" name="Theme.AppCompat.DayNight" id="0x7f11013f" />
    <public type="style" name="Theme.AppCompat.DayNight.DarkActionBar" id="0x7f110140" />
    <public type="style" name="Theme.AppCompat.DayNight.Dialog" id="0x7f110141" />
    <public type="style" name="Theme.AppCompat.DayNight.Dialog.Alert" id="0x7f110142" />
    <public type="style" name="Theme.AppCompat.DayNight.Dialog.MinWidth" id="0x7f110143" />
    <public type="style" name="Theme.AppCompat.DayNight.DialogWhenLarge" id="0x7f110144" />
    <public type="style" name="Theme.AppCompat.DayNight.NoActionBar" id="0x7f110145" />
    <public type="style" name="Theme.AppCompat.Dialog" id="0x7f110146" />
    <public type="style" name="Theme.AppCompat.Dialog.Alert" id="0x7f110147" />
    <public type="style" name="Theme.AppCompat.Dialog.MinWidth" id="0x7f110148" />
    <public type="style" name="Theme.AppCompat.DialogWhenLarge" id="0x7f110149" />
    <public type="style" name="Theme.AppCompat.Light" id="0x7f11014a" />
    <public type="style" name="Theme.AppCompat.Light.DarkActionBar" id="0x7f11014b" />
    <public type="style" name="Theme.AppCompat.Light.Dialog" id="0x7f11014c" />
    <public type="style" name="Theme.AppCompat.Light.Dialog.Alert" id="0x7f11014d" />
    <public type="style" name="Theme.AppCompat.Light.Dialog.MinWidth" id="0x7f11014e" />
    <public type="style" name="Theme.AppCompat.Light.DialogWhenLarge" id="0x7f11014f" />
    <public type="style" name="Theme.AppCompat.Light.NoActionBar" id="0x7f110150" />
    <public type="style" name="Theme.AppCompat.NoActionBar" id="0x7f110151" />
    <public type="style" name="Theme.Design" id="0x7f110152" />
    <public type="style" name="Theme.Design.BottomSheetDialog" id="0x7f110153" />
    <public type="style" name="Theme.Design.Light" id="0x7f110154" />
    <public type="style" name="Theme.Design.Light.BottomSheetDialog" id="0x7f110155" />
    <public type="style" name="Theme.Design.Light.NoActionBar" id="0x7f110156" />
    <public type="style" name="Theme.Design.NoActionBar" id="0x7f110157" />
    <public type="style" name="Theme.MaterialComponents" id="0x7f110158" />
    <public type="style" name="Theme.MaterialComponents.BottomSheetDialog" id="0x7f110159" />
    <public type="style" name="Theme.MaterialComponents.Bridge" id="0x7f11015a" />
    <public type="style" name="Theme.MaterialComponents.CompactMenu" id="0x7f11015b" />
    <public type="style" name="Theme.MaterialComponents.Dialog" id="0x7f11015c" />
    <public type="style" name="Theme.MaterialComponents.Dialog.Alert" id="0x7f11015d" />
    <public type="style" name="Theme.MaterialComponents.Dialog.MinWidth" id="0x7f11015e" />
    <public type="style" name="Theme.MaterialComponents.DialogWhenLarge" id="0x7f11015f" />
    <public type="style" name="Theme.MaterialComponents.Light" id="0x7f110160" />
    <public type="style" name="Theme.MaterialComponents.Light.BottomSheetDialog" id="0x7f110161" />
    <public type="style" name="Theme.MaterialComponents.Light.Bridge" id="0x7f110162" />
    <public type="style" name="Theme.MaterialComponents.Light.DarkActionBar" id="0x7f110163" />
    <public type="style" name="Theme.MaterialComponents.Light.DarkActionBar.Bridge" id="0x7f110164" />
    <public type="style" name="Theme.MaterialComponents.Light.Dialog" id="0x7f110165" />
    <public type="style" name="Theme.MaterialComponents.Light.Dialog.Alert" id="0x7f110166" />
    <public type="style" name="Theme.MaterialComponents.Light.Dialog.MinWidth" id="0x7f110167" />
    <public type="style" name="Theme.MaterialComponents.Light.DialogWhenLarge" id="0x7f110168" />
    <public type="style" name="Theme.MaterialComponents.Light.NoActionBar" id="0x7f110169" />
    <public type="style" name="Theme.MaterialComponents.Light.NoActionBar.Bridge" id="0x7f11016a" />
    <public type="style" name="Theme.MaterialComponents.NoActionBar" id="0x7f11016b" />
    <public type="style" name="Theme.MaterialComponents.NoActionBar.Bridge" id="0x7f11016c" />
    <public type="style" name="ThemeOverlay.AppCompat" id="0x7f11016d" />
    <public type="style" name="ThemeOverlay.AppCompat.ActionBar" id="0x7f11016e" />
    <public type="style" name="ThemeOverlay.AppCompat.Dark" id="0x7f11016f" />
    <public type="style" name="ThemeOverlay.AppCompat.Dark.ActionBar" id="0x7f110170" />
    <public type="style" name="ThemeOverlay.AppCompat.Dialog" id="0x7f110171" />
    <public type="style" name="ThemeOverlay.AppCompat.Dialog.Alert" id="0x7f110172" />
    <public type="style" name="ThemeOverlay.AppCompat.Light" id="0x7f110173" />
    <public type="style" name="ThemeOverlay.MaterialComponents" id="0x7f110174" />
    <public type="style" name="ThemeOverlay.MaterialComponents.ActionBar" id="0x7f110175" />
    <public type="style" name="ThemeOverlay.MaterialComponents.Dark" id="0x7f110176" />
    <public type="style" name="ThemeOverlay.MaterialComponents.Dark.ActionBar" id="0x7f110177" />
    <public type="style" name="ThemeOverlay.MaterialComponents.Dialog" id="0x7f110178" />
    <public type="style" name="ThemeOverlay.MaterialComponents.Dialog.Alert" id="0x7f110179" />
    <public type="style" name="ThemeOverlay.MaterialComponents.Light" id="0x7f11017a" />
    <public type="style" name="ThemeOverlay.MaterialComponents.TextInputEditText" id="0x7f11017b" />
    <public type="style" name="ThemeOverlay.MaterialComponents.TextInputEditText.FilledBox" id="0x7f11017c" />
    <public type="style" name="ThemeOverlay.MaterialComponents.TextInputEditText.FilledBox.Dense" id="0x7f11017d" />
    <public type="style" name="ThemeOverlay.MaterialComponents.TextInputEditText.OutlinedBox" id="0x7f11017e" />
    <public type="style" name="ThemeOverlay.MaterialComponents.TextInputEditText.OutlinedBox.Dense" id="0x7f11017f" />
    <public type="style" name="Widget.AppCompat.ActionBar" id="0x7f110180" />
    <public type="style" name="Widget.AppCompat.ActionBar.Solid" id="0x7f110181" />
    <public type="style" name="Widget.AppCompat.ActionBar.TabBar" id="0x7f110182" />
    <public type="style" name="Widget.AppCompat.ActionBar.TabText" id="0x7f110183" />
    <public type="style" name="Widget.AppCompat.ActionBar.TabView" id="0x7f110184" />
    <public type="style" name="Widget.AppCompat.ActionButton" id="0x7f110185" />
    <public type="style" name="Widget.AppCompat.ActionButton.CloseMode" id="0x7f110186" />
    <public type="style" name="Widget.AppCompat.ActionButton.Overflow" id="0x7f110187" />
    <public type="style" name="Widget.AppCompat.ActionMode" id="0x7f110188" />
    <public type="style" name="Widget.AppCompat.ActivityChooserView" id="0x7f110189" />
    <public type="style" name="Widget.AppCompat.AutoCompleteTextView" id="0x7f11018a" />
    <public type="style" name="Widget.AppCompat.Button" id="0x7f11018b" />
    <public type="style" name="Widget.AppCompat.Button.Borderless" id="0x7f11018c" />
    <public type="style" name="Widget.AppCompat.Button.Borderless.Colored" id="0x7f11018d" />
    <public type="style" name="Widget.AppCompat.Button.ButtonBar.AlertDialog" id="0x7f11018e" />
    <public type="style" name="Widget.AppCompat.Button.Colored" id="0x7f11018f" />
    <public type="style" name="Widget.AppCompat.Button.Small" id="0x7f110190" />
    <public type="style" name="Widget.AppCompat.ButtonBar" id="0x7f110191" />
    <public type="style" name="Widget.AppCompat.ButtonBar.AlertDialog" id="0x7f110192" />
    <public type="style" name="Widget.AppCompat.CompoundButton.CheckBox" id="0x7f110193" />
    <public type="style" name="Widget.AppCompat.CompoundButton.RadioButton" id="0x7f110194" />
    <public type="style" name="Widget.AppCompat.CompoundButton.Switch" id="0x7f110195" />
    <public type="style" name="Widget.AppCompat.DrawerArrowToggle" id="0x7f110196" />
    <public type="style" name="Widget.AppCompat.DropDownItem.Spinner" id="0x7f110197" />
    <public type="style" name="Widget.AppCompat.EditText" id="0x7f110198" />
    <public type="style" name="Widget.AppCompat.ImageButton" id="0x7f110199" />
    <public type="style" name="Widget.AppCompat.Light.ActionBar" id="0x7f11019a" />
    <public type="style" name="Widget.AppCompat.Light.ActionBar.Solid" id="0x7f11019b" />
    <public type="style" name="Widget.AppCompat.Light.ActionBar.Solid.Inverse" id="0x7f11019c" />
    <public type="style" name="Widget.AppCompat.Light.ActionBar.TabBar" id="0x7f11019d" />
    <public type="style" name="Widget.AppCompat.Light.ActionBar.TabBar.Inverse" id="0x7f11019e" />
    <public type="style" name="Widget.AppCompat.Light.ActionBar.TabText" id="0x7f11019f" />
    <public type="style" name="Widget.AppCompat.Light.ActionBar.TabText.Inverse" id="0x7f1101a0" />
    <public type="style" name="Widget.AppCompat.Light.ActionBar.TabView" id="0x7f1101a1" />
    <public type="style" name="Widget.AppCompat.Light.ActionBar.TabView.Inverse" id="0x7f1101a2" />
    <public type="style" name="Widget.AppCompat.Light.ActionButton" id="0x7f1101a3" />
    <public type="style" name="Widget.AppCompat.Light.ActionButton.CloseMode" id="0x7f1101a4" />
    <public type="style" name="Widget.AppCompat.Light.ActionButton.Overflow" id="0x7f1101a5" />
    <public type="style" name="Widget.AppCompat.Light.ActionMode.Inverse" id="0x7f1101a6" />
    <public type="style" name="Widget.AppCompat.Light.ActivityChooserView" id="0x7f1101a7" />
    <public type="style" name="Widget.AppCompat.Light.AutoCompleteTextView" id="0x7f1101a8" />
    <public type="style" name="Widget.AppCompat.Light.DropDownItem.Spinner" id="0x7f1101a9" />
    <public type="style" name="Widget.AppCompat.Light.ListPopupWindow" id="0x7f1101aa" />
    <public type="style" name="Widget.AppCompat.Light.ListView.DropDown" id="0x7f1101ab" />
    <public type="style" name="Widget.AppCompat.Light.PopupMenu" id="0x7f1101ac" />
    <public type="style" name="Widget.AppCompat.Light.PopupMenu.Overflow" id="0x7f1101ad" />
    <public type="style" name="Widget.AppCompat.Light.SearchView" id="0x7f1101ae" />
    <public type="style" name="Widget.AppCompat.Light.Spinner.DropDown.ActionBar" id="0x7f1101af" />
    <public type="style" name="Widget.AppCompat.ListMenuView" id="0x7f1101b0" />
    <public type="style" name="Widget.AppCompat.ListPopupWindow" id="0x7f1101b1" />
    <public type="style" name="Widget.AppCompat.ListView" id="0x7f1101b2" />
    <public type="style" name="Widget.AppCompat.ListView.DropDown" id="0x7f1101b3" />
    <public type="style" name="Widget.AppCompat.ListView.Menu" id="0x7f1101b4" />
    <public type="style" name="Widget.AppCompat.PopupMenu" id="0x7f1101b5" />
    <public type="style" name="Widget.AppCompat.PopupMenu.Overflow" id="0x7f1101b6" />
    <public type="style" name="Widget.AppCompat.PopupWindow" id="0x7f1101b7" />
    <public type="style" name="Widget.AppCompat.ProgressBar" id="0x7f1101b8" />
    <public type="style" name="Widget.AppCompat.ProgressBar.Horizontal" id="0x7f1101b9" />
    <public type="style" name="Widget.AppCompat.RatingBar" id="0x7f1101ba" />
    <public type="style" name="Widget.AppCompat.RatingBar.Indicator" id="0x7f1101bb" />
    <public type="style" name="Widget.AppCompat.RatingBar.Small" id="0x7f1101bc" />
    <public type="style" name="Widget.AppCompat.SearchView" id="0x7f1101bd" />
    <public type="style" name="Widget.AppCompat.SearchView.ActionBar" id="0x7f1101be" />
    <public type="style" name="Widget.AppCompat.SeekBar" id="0x7f1101bf" />
    <public type="style" name="Widget.AppCompat.SeekBar.Discrete" id="0x7f1101c0" />
    <public type="style" name="Widget.AppCompat.Spinner" id="0x7f1101c1" />
    <public type="style" name="Widget.AppCompat.Spinner.DropDown" id="0x7f1101c2" />
    <public type="style" name="Widget.AppCompat.Spinner.DropDown.ActionBar" id="0x7f1101c3" />
    <public type="style" name="Widget.AppCompat.Spinner.Underlined" id="0x7f1101c4" />
    <public type="style" name="Widget.AppCompat.TextView.SpinnerItem" id="0x7f1101c5" />
    <public type="style" name="Widget.AppCompat.Toolbar" id="0x7f1101c6" />
    <public type="style" name="Widget.AppCompat.Toolbar.Button.Navigation" id="0x7f1101c7" />
    <public type="style" name="Widget.Compat.NotificationActionContainer" id="0x7f1101c8" />
    <public type="style" name="Widget.Compat.NotificationActionText" id="0x7f1101c9" />
    <public type="style" name="Widget.Design.AppBarLayout" id="0x7f1101ca" />
    <public type="style" name="Widget.Design.BottomNavigationView" id="0x7f1101cb" />
    <public type="style" name="Widget.Design.BottomSheet.Modal" id="0x7f1101cc" />
    <public type="style" name="Widget.Design.CollapsingToolbar" id="0x7f1101cd" />
    <public type="style" name="Widget.Design.FloatingActionButton" id="0x7f1101ce" />
    <public type="style" name="Widget.Design.NavigationView" id="0x7f1101cf" />
    <public type="style" name="Widget.Design.ScrimInsetsFrameLayout" id="0x7f1101d0" />
    <public type="style" name="Widget.Design.Snackbar" id="0x7f1101d1" />
    <public type="style" name="Widget.Design.TabLayout" id="0x7f1101d2" />
    <public type="style" name="Widget.Design.TextInputLayout" id="0x7f1101d3" />
    <public type="style" name="Widget.MaterialComponents.BottomAppBar" id="0x7f1101d4" />
    <public type="style" name="Widget.MaterialComponents.BottomAppBar.Colored" id="0x7f1101d5" />
    <public type="style" name="Widget.MaterialComponents.BottomNavigationView" id="0x7f1101d6" />
    <public type="style" name="Widget.MaterialComponents.BottomNavigationView.Colored" id="0x7f1101d7" />
    <public type="style" name="Widget.MaterialComponents.BottomSheet.Modal" id="0x7f1101d8" />
    <public type="style" name="Widget.MaterialComponents.Button" id="0x7f1101d9" />
    <public type="style" name="Widget.MaterialComponents.Button.Icon" id="0x7f1101da" />
    <public type="style" name="Widget.MaterialComponents.Button.OutlinedButton" id="0x7f1101db" />
    <public type="style" name="Widget.MaterialComponents.Button.OutlinedButton.Icon" id="0x7f1101dc" />
    <public type="style" name="Widget.MaterialComponents.Button.TextButton" id="0x7f1101dd" />
    <public type="style" name="Widget.MaterialComponents.Button.TextButton.Dialog" id="0x7f1101de" />
    <public type="style" name="Widget.MaterialComponents.Button.TextButton.Dialog.Icon" id="0x7f1101df" />
    <public type="style" name="Widget.MaterialComponents.Button.TextButton.Icon" id="0x7f1101e0" />
    <public type="style" name="Widget.MaterialComponents.Button.UnelevatedButton" id="0x7f1101e1" />
    <public type="style" name="Widget.MaterialComponents.Button.UnelevatedButton.Icon" id="0x7f1101e2" />
    <public type="style" name="Widget.MaterialComponents.CardView" id="0x7f1101e3" />
    <public type="style" name="Widget.MaterialComponents.Chip.Action" id="0x7f1101e4" />
    <public type="style" name="Widget.MaterialComponents.Chip.Choice" id="0x7f1101e5" />
    <public type="style" name="Widget.MaterialComponents.Chip.Entry" id="0x7f1101e6" />
    <public type="style" name="Widget.MaterialComponents.Chip.Filter" id="0x7f1101e7" />
    <public type="style" name="Widget.MaterialComponents.ChipGroup" id="0x7f1101e8" />
    <public type="style" name="Widget.MaterialComponents.FloatingActionButton" id="0x7f1101e9" />
    <public type="style" name="Widget.MaterialComponents.NavigationView" id="0x7f1101ea" />
    <public type="style" name="Widget.MaterialComponents.Snackbar" id="0x7f1101eb" />
    <public type="style" name="Widget.MaterialComponents.Snackbar.FullWidth" id="0x7f1101ec" />
    <public type="style" name="Widget.MaterialComponents.TabLayout" id="0x7f1101ed" />
    <public type="style" name="Widget.MaterialComponents.TabLayout.Colored" id="0x7f1101ee" />
    <public type="style" name="Widget.MaterialComponents.TextInputEditText.FilledBox" id="0x7f1101ef" />
    <public type="style" name="Widget.MaterialComponents.TextInputEditText.FilledBox.Dense" id="0x7f1101f0" />
    <public type="style" name="Widget.MaterialComponents.TextInputEditText.OutlinedBox" id="0x7f1101f1" />
    <public type="style" name="Widget.MaterialComponents.TextInputEditText.OutlinedBox.Dense" id="0x7f1101f2" />
    <public type="style" name="Widget.MaterialComponents.TextInputLayout.FilledBox" id="0x7f1101f3" />
    <public type="style" name="Widget.MaterialComponents.TextInputLayout.FilledBox.Dense" id="0x7f1101f4" />
    <public type="style" name="Widget.MaterialComponents.TextInputLayout.OutlinedBox" id="0x7f1101f5" />
    <public type="style" name="Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense" id="0x7f1101f6" />
    <public type="style" name="Widget.MaterialComponents.Toolbar" id="0x7f1101f7" />
    <public type="style" name="Widget.Support.CoordinatorLayout" id="0x7f1101f8" />
    <public type="style" name="MinimalButton" id="0x7f1101f9" />
    <public type="style" name="MinimalCard" id="0x7f1101fa" />
    <public type="style" name="MinimalText" id="0x7f1101fb" />
    <public type="style" name="Base.V22.Theme.AppCompat" id="0x7f110078" />
    <public type="style" name="Base.V22.Theme.AppCompat.Light" id="0x7f110079" />
    <public type="style" name="Base.V23.Theme.AppCompat" id="0x7f11007a" />
    <public type="style" name="Base.V23.Theme.AppCompat.Light" id="0x7f11007b" />
    <public type="style" name="Platform.V25.AppCompat" id="0x7f1100d4" />
    <public type="style" name="Platform.V25.AppCompat.Light" id="0x7f1100d5" />
    <public type="style" name="Base.V26.Theme.AppCompat" id="0x7f11007c" />
    <public type="style" name="Base.V26.Theme.AppCompat.Light" id="0x7f11007d" />
    <public type="style" name="Base.V26.Widget.AppCompat.Toolbar" id="0x7f11007e" />
    <public type="style" name="Base.V28.Theme.AppCompat" id="0x7f11007f" />
    <public type="style" name="Base.V28.Theme.AppCompat.Light" id="0x7f110080" />
    <public type="xml" name="net_template" id="0x7f130000" />
    <public type="xml" name="net_template_default" id="0x7f130001" />
    <public type="xml" name="pref_main" id="0x7f130002" />
</resources>