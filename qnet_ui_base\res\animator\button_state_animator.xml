<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- 按下状态动画 - 紫蓝色主题优化 -->
    <item android:state_pressed="true">
        <set>
            <objectAnimator
                android:propertyName="scaleX"
                android:duration="120"
                android:valueTo="0.92"
                android:valueType="floatType"
                android:interpolator="@android:anim/decelerate_interpolator" />
            <objectAnimator
                android:propertyName="scaleY"
                android:duration="120"
                android:valueTo="0.92"
                android:valueType="floatType"
                android:interpolator="@android:anim/decelerate_interpolator" />
            <objectAnimator
                android:propertyName="elevation"
                android:duration="120"
                android:valueTo="2dp"
                android:valueType="floatType" />
            <objectAnimator
                android:propertyName="alpha"
                android:duration="120"
                android:valueTo="0.8"
                android:valueType="floatType" />
        </set>
    </item>
    
    <!-- 正常状态动画 - 紫蓝色主题优化 -->
    <item>
        <set>
            <objectAnimator
                android:propertyName="scaleX"
                android:duration="180"
                android:valueTo="1.0"
                android:valueType="floatType"
                android:interpolator="@android:anim/overshoot_interpolator" />
            <objectAnimator
                android:propertyName="scaleY"
                android:duration="180"
                android:valueTo="1.0"
                android:valueType="floatType"
                android:interpolator="@android:anim/overshoot_interpolator" />
            <objectAnimator
                android:propertyName="elevation"
                android:duration="180"
                android:valueTo="6dp"
                android:valueType="floatType" />
            <objectAnimator
                android:propertyName="alpha"
                android:duration="180"
                android:valueTo="1.0"
                android:valueType="floatType" />
        </set>
    </item>
    
</selector>
