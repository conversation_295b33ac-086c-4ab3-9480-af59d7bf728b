<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/meteor_background_gradient">
    
    <!-- 静态流星粒子 - 测试可见性 -->
    <ImageView
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:src="@drawable/meteor_particle"
        android:layout_gravity="start|top"
        android:layout_marginLeft="50dp"
        android:layout_marginTop="100dp" />
        
    <ImageView
        android:layout_width="16dp"
        android:layout_height="16dp"
        android:src="@drawable/meteor_particle"
        android:layout_gravity="end|top"
        android:layout_marginRight="80dp"
        android:layout_marginTop="150dp" />
        
    <ImageView
        android:layout_width="18dp"
        android:layout_height="18dp"
        android:src="@drawable/meteor_particle"
        android:layout_gravity="center_horizontal|top"
        android:layout_marginTop="200dp" />
        
    <!-- 静态流星尾迹 -->
    <ImageView
        android:layout_width="40dp"
        android:layout_height="4dp"
        android:src="@drawable/meteor_trail"
        android:layout_gravity="start|center_vertical"
        android:layout_marginLeft="30dp"
        android:rotation="45" />
        
    <ImageView
        android:layout_width="35dp"
        android:layout_height="3dp"
        android:src="@drawable/meteor_trail"
        android:layout_gravity="end|center_vertical"
        android:layout_marginRight="60dp"
        android:rotation="-30" />
        
    <ImageView
        android:layout_width="45dp"
        android:layout_height="5dp"
        android:src="@drawable/meteor_trail"
        android:layout_gravity="center"
        android:rotation="15" />
        
</FrameLayout>
