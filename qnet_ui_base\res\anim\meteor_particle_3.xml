<?xml version="1.0" encoding="utf-8"?>
<set xmlns:android="http://schemas.android.com/apk/res/android"
    android:repeatCount="infinite"
    android:repeatMode="restart">
    
    <!-- 流星轨迹移动 - 垂直方向 -->
    <translate
        android:fromXDelta="50%"
        android:toXDelta="80%"
        android:fromYDelta="-20%"
        android:toYDelta="120%"
        android:duration="3500"
        android:startOffset="2000"
        android:interpolator="@android:anim/linear_interpolator" />
        
    <!-- 流星闪烁效果 -->
    <alpha
        android:fromAlpha="0.0"
        android:toAlpha="0.9"
        android:duration="350"
        android:startOffset="2000"
        android:interpolator="@android:anim/accelerate_interpolator" />
        
    <alpha
        android:fromAlpha="0.9"
        android:toAlpha="0.1"
        android:duration="2800"
        android:startOffset="2350"
        android:interpolator="@android:anim/decelerate_interpolator" />
        
    <alpha
        android:fromAlpha="0.1"
        android:toAlpha="0.0"
        android:duration="350"
        android:startOffset="5150"
        android:interpolator="@android:anim/accelerate_interpolator" />
        
    <!-- 流星缩放效果 -->
    <scale
        android:fromXScale="0.4"
        android:toXScale="0.9"
        android:fromYScale="0.4"
        android:toYScale="0.9"
        android:pivotX="50%"
        android:pivotY="50%"
        android:duration="350"
        android:startOffset="2000"
        android:interpolator="@android:anim/overshoot_interpolator" />
        
    <scale
        android:fromXScale="0.9"
        android:toXScale="0.5"
        android:fromYScale="0.9"
        android:toYScale="0.5"
        android:pivotX="50%"
        android:pivotY="50%"
        android:duration="3150"
        android:startOffset="2350"
        android:interpolator="@android:anim/decelerate_interpolator" />
        
</set>
