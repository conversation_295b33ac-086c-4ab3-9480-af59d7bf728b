package com.tencent.qnet.ui;

import android.annotation.SuppressLint;
import android.app.AlertDialog;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.os.Binder;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.provider.Settings;
import android.support.annotation.RequiresApi;
import android.support.v4.internal.view.SupportMenu;
import android.support.v7.app.AppCompatActivity;
import android.support.v7.widget.Toolbar;
import android.util.Log;
import android.view.KeyEvent;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.widget.AdapterView;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ListAdapter;
import android.widget.ListView;
import android.widget.TextView;
import android.widget.Toast;
import android.view.animation.Animation;
import android.view.animation.TranslateAnimation;
import android.view.animation.AlphaAnimation;
import android.view.animation.ScaleAnimation;
import com.tencent.qnet.common.SystemUtil;
import com.tencent.qnet.core.LocalVpnService;
import com.tencent.qnet.global.GlobalSettings;
import com.tencent.qnet.global.QnetApplication;
import com.tencent.qnet.global.QnetGlobalDefine;
import com.tencent.qnet.global.ServerHelper;
import com.tencent.qnet.net.NetConfigChange;
import com.tencent.qnet.net.NetProfile;
import com.tencent.qnet.net.NetworkConfig;
import com.tencent.qnet.profile.ProposalManager;
import com.tencent.qnet.rick.R;
import com.tencent.qnet.ui.float_window.FWService;
import com.tencent.qnet.ui.login.LoginActivity;
import com.tencent.qnet.ui.login.manager.LoginManager;
import com.tencent.qnet.ui.profile.NetEditActivity;
import com.tencent.qnet.ui.profile.NetProfileAdapter;
import com.tencent.qnet.ui.profile.NetProfileItem;
import com.tencent.qnet.ui.settings.SettingsActivity;
import com.tencent.qnet.ui.MeteorAnimationController;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import np.C0026;
/* loaded from: classes.dex */
public class MainActivity extends AppCompatActivity implements LocalVpnService.onStatusChangedListener {
    private static final int START_VPN_SERVICE_REQUEST_CODE = 2017;
    private static MainActivity mainActivity;
    private Button mButtonTest;
    private boolean mIsRunning;
    private String m_currentEditID;
    private NetProfileItem m_currentSelectedProfile;
    private Menu m_menu;
    ListView m_profileListView;
    public Handler m_profile_sync_handler;
    private List<NetProfile> profileList;
    private MeteorAnimationController meteorController;

    public MainActivity() {
        mainActivity = this;
    }

    private void UpdateNetworkConfig3() {
        try {
            NetworkConfig Instnace = NetworkConfig.Instnace();
            if (this.m_currentSelectedProfile != null) {
                NetProfile profile = this.m_currentSelectedProfile.getProfile();
                List<String> sortedList = NetProfile.getSortedList();
                for (int i = 0; i < sortedList.size(); i++) {
                    String str = sortedList.get(i);
                    Field field = NetworkConfig.class.getField(str);
                    if (field != null) {
                        field.setInt(Instnace, profile.getParamIntValue(str));
                    }
                }
                NetConfigChange.Instance.setCurrentProfile(profile);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void UpdateVPNStatusChanged(boolean z) {
        setButtonTextStatus(z);
    }

    @RequiresApi(api = 19)
    private static boolean checkOps(Context context) {
        Method method;
        try {
            Object systemService = context.getSystemService("appops");
            if (systemService == null || (method = systemService.getClass().getMethod("checkOp", Integer.TYPE, Integer.TYPE, String.class)) == null) {
                return false;
            }
            return ((Integer) method.invoke(systemService, 24, Integer.valueOf(Binder.getCallingUid()), context.getPackageName())).intValue() == 0;
        } catch (Exception unused) {
            return false;
        }
    }

    /* JADX WARN: Type inference failed for: r0v0, types: [com.tencent.qnet.ui.MainActivity$11] */
    private void checkVersion() {
        new Thread() { // from class: com.tencent.qnet.ui.MainActivity.11
            @Override // java.lang.Thread, java.lang.Runnable
            public void run() {
                ServerHelper.QNET_SERVER_IP = ServerHelper.getIPArrFromHost();
                final int qnetMinVersion = ServerHelper.getQnetMinVersion();
                MainActivity.this.runOnUiThread(new Runnable() { // from class: com.tencent.qnet.ui.MainActivity.11.1
                    @Override // java.lang.Runnable
                    public void run() {
                        try {
                            if (qnetMinVersion <= 0) {
                                MainActivity.this.showCheckVersionDialog(MainActivity.this.getResources().getString(R.string.versionUpdateHttpErrorTips));
                                return;
                            }
                            if (qnetMinVersion > SystemUtil.getAppVersionInfo(QnetApplication.getContext()).versionCode) {
                                MainActivity.this.showCheckVersionDialog(MainActivity.this.getResources().getString(R.string.versionUpdateTips));
                                return;
                            }
                            if (!ProposalManager.GetInstance().IsProposalLoaded()) {
                                ProposalManager.GetInstance().LoadLocalProposal();
                            }
                            ProposalManager.GetInstance().SetProposalUser(GlobalSettings.getInstance().getUserName());
                            ProposalManager.GetInstance().DownLoadMyProposals();
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                });
            }
        }.start();
    }

    @SuppressLint({"NewApi"})
    private boolean check_overlays_privilege() {
        if (Build.VERSION.SDK_INT >= 23) {
            if (!Settings.canDrawOverlays(this)) {
                Intent intent = new Intent("android.settings.action.MANAGE_OVERLAY_PERMISSION");
                Toast.makeText(this, "需要取得权限以使用悬浮窗", 0).show();
                startActivity(intent);
                return false;
            }
        } else if (Build.VERSION.SDK_INT >= 19 && !checkOps(this)) {
            return true;
        }
        return true;
    }

    public static MainActivity getMainActivity() {
        return mainActivity;
    }

    private void initAppManager() {
        new AppProxyManager(this);
        updateTestProgramUI();
        findViewById(R.id.layout_process).setOnClickListener(new View.OnClickListener() { // from class: com.tencent.qnet.ui.MainActivity.5
            @Override // android.view.View.OnClickListener
            public void onClick(View view) {
                if (LocalVpnService.IsRunning) {
                    new AlertDialog.Builder(MainActivity.this).setMessage(MainActivity.this.getResources().getString(R.string.msg_vpn_is_running)).setPositiveButton(view.getResources().getString(R.string.dialog_btn_OK), new DialogInterface.OnClickListener() { // from class: com.tencent.qnet.ui.MainActivity.5.1
                        @Override // android.content.DialogInterface.OnClickListener
                        public void onClick(DialogInterface dialogInterface, int i) {
                        }
                    }).create().show();
                } else {
                    MainActivity.this.startActivityForResult(new Intent(MainActivity.this, AppManager.class), AppManager.REQUEST_CODE);
                }
            }
        });
    }

    private void initButtonTest() {
        this.mButtonTest = (Button) findViewById(R.id.buttonTest);
        this.mButtonTest.setOnClickListener(new View.OnClickListener() { // from class: com.tencent.qnet.ui.MainActivity.4
            @Override // android.view.View.OnClickListener
            public void onClick(View view) {
                MainActivity.this.onButtonTestClicked();
            }
        });
        setButtonTextStatus(LocalVpnService.IsRunning);
    }

    private void initGlobalSettings() {
        try {
            GlobalSettings.getInstance().appVersion = SystemUtil.getAppVersionInfo(QnetApplication.getContext()).versionName;
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void initLogin() {
        reportLogin();
        checkVersion();
    }

    private void initNetTemplateUI() {
        this.m_profileListView = (ListView) findViewById(R.id.net_config_list);
        this.m_profileListView.setEmptyView(findViewById(16908292));
        refreshNetProfilesList();
        this.m_profileListView.setOnItemClickListener(new AdapterView.OnItemClickListener() { // from class: com.tencent.qnet.ui.MainActivity.9
            @Override // android.widget.AdapterView.OnItemClickListener
            public void onItemClick(AdapterView<?> adapterView, View view, int i, long j) {
                NetProfileItem netProfileItem = (NetProfileItem) ((NetProfileAdapter) MainActivity.this.m_profileListView.getAdapter()).getItem(i);
                if (MainActivity.this.m_currentSelectedProfile == null) {
                    MainActivity.this.onNetProfileChanged(netProfileItem, true);
                } else if (view != MainActivity.this.m_currentSelectedProfile.getView()) {
                    MainActivity.this.onNetProfileChanged(netProfileItem, true);
                }
            }
        });
        ((LinearLayout) findViewById(R.id.btnAddProfile)).setOnClickListener(new View.OnClickListener() { // from class: com.tencent.qnet.ui.MainActivity.10
            @Override // android.view.View.OnClickListener
            public void onClick(View view) {
                MainActivity mainActivity2 = MainActivity.this;
                Intent intent = new Intent(mainActivity2, NetEditActivity.class);
                intent.putExtra("ID", "");
                intent.putExtra("Select", false);
                mainActivity2.startActivityForResult(intent, NetEditActivity.ACTIVITY_CODE);
            }
        });
    }

    private void initReleaseMode() {
        QnetApplication qnetApplication = (QnetApplication) getApplication();
        if (QnetApplication.ReleaseMode()) {
            return;
        }
        TextView textView = (TextView) findViewById(R.id.toolbar_title);
        textView.setText(String.format("%s[test]", textView.getText()));
        textView.setTextColor(SupportMenu.CATEGORY_MASK);
    }

    private void initService() {
        LocalVpnService.addOnStatusChangedListener(this);
    }

    private void initSyncHandler() {
        this.m_profile_sync_handler = new Handler() { // from class: com.tencent.qnet.ui.MainActivity.2
            @Override // android.os.Handler
            public void handleMessage(Message message) {
                int i = message.arg1;
                String str = (String) message.obj;
                if (i == QnetGlobalDefine.QnetMessageType.MS_NET_PROPOSAL_LOADED.ordinal()) {
                    MainActivity.this.refreshNetProfilesList();
                } else if (i == QnetGlobalDefine.QnetMessageType.MS_NET_PROPOSAL_LOAD_FAILED.ordinal()) {
                    MainActivity.this.showNormalDialog(str);
                }
            }
        };
    }

    private void initToolbar() {
        setSupportActionBar((Toolbar) findViewById(R.id.toolbar));
        getSupportActionBar().setDisplayShowTitleEnabled(false);
    }

    private void initUI() {
        initReleaseMode();
        initSyncHandler();
        initToolbar();
        initAppManager();
        initButtonTest();
        initNetTemplateUI();
        initService();
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void onButtonTestClicked() {
        if (AppProxyManager.Instance.getProxyApp() == null) {
            new AlertDialog.Builder(this).setMessage(getResources().getString(R.string.msg_select_progress)).setPositiveButton(getResources().getString(R.string.dialog_btn_OK), new DialogInterface.OnClickListener() { // from class: com.tencent.qnet.ui.MainActivity.6
                @Override // android.content.DialogInterface.OnClickListener
                public void onClick(DialogInterface dialogInterface, int i) {
                }
            }).create().show();
        } else if (this.m_currentSelectedProfile == null) {
            new AlertDialog.Builder(this).setMessage(getResources().getString(R.string.msg_select_profile)).setPositiveButton(getResources().getString(R.string.dialog_btn_OK), new DialogInterface.OnClickListener() { // from class: com.tencent.qnet.ui.MainActivity.7
                @Override // android.content.DialogInterface.OnClickListener
                public void onClick(DialogInterface dialogInterface, int i) {
                }
            }).create().show();
        } else if (check_overlays_privilege()) {
            this.mIsRunning = !this.mIsRunning;
            if (LocalVpnService.IsRunning != this.mIsRunning) {
                if (!this.mIsRunning) {
                    LocalVpnService.IsRunning = false;
                    return;
                }
                Intent prepare = LocalVpnService.prepare(this);
                if (prepare == null) {
                    startVPNService();
                } else {
                    startActivityForResult(prepare, START_VPN_SERVICE_REQUEST_CODE);
                }
            }
        }
    }

    private void onEditActivityReturn(Intent intent) {
        this.m_currentEditID = intent.getExtras().getString("ID");
        refreshNetProfilesList();
    }

    private void onNetConfigChanged(boolean z) {
        UpdateNetworkConfig3();
        if (LocalVpnService.IsRunning) {
            Toast.makeText(this, String.format("[%s] %s", this.m_currentSelectedProfile.getProfile().Name, getResources().getString(R.string.toast_net_config_activated)), 0).show();
        }
        if (FWService.getInstance() == null || !z) {
            return;
        }
        FWService.getInstance().onProfileChanged();
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void onNetProfileChanged(NetProfileItem netProfileItem, boolean z) {
        if (this.m_currentSelectedProfile != null) {
            this.m_currentSelectedProfile.getProfile().Selected = false;
            ProposalManager.GetInstance().EditProposal(this.m_currentSelectedProfile.getProfile(), false, true);
            setProfileSelected(this.m_currentSelectedProfile.getView(), false);
        }
        this.m_currentSelectedProfile = netProfileItem;
        this.m_currentSelectedProfile.getProfile().Selected = true;
        ProposalManager.GetInstance().EditProposal(this.m_currentSelectedProfile.getProfile(), false, true);
        setProfileSelected(this.m_currentSelectedProfile.getView(), true);
        onNetConfigChanged(z);
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void refreshNetProfilesList() {
        if (this.profileList != null) {
            this.profileList.clear();
        }
        this.profileList = ProposalManager.GetInstance().GetProposal();
        List<NetProfileItem> CovertToProfileItemList = NetProfileAdapter.CovertToProfileItemList(this.profileList);
        NetProfileAdapter netProfileAdapter = (NetProfileAdapter) this.m_profileListView.getAdapter();
        if (netProfileAdapter != null) {
            netProfileAdapter.setItems(CovertToProfileItemList);
        } else {
            netProfileAdapter = new NetProfileAdapter(this, CovertToProfileItemList);
        }
        this.m_profileListView.setAdapter((ListAdapter) netProfileAdapter);

        // 恢复之前选中的配置
        restoreSelectedProfile(CovertToProfileItemList);

        if (FWService.getInstance() != null) {
            FWService.getInstance().onProfileChanged();
        }
    }

    /* 恢复选中的配置状态 */
    private void restoreSelectedProfile(List<NetProfileItem> profileItems) {
        for (NetProfileItem item : profileItems) {
            if (item.getProfile().Selected) {
                this.m_currentSelectedProfile = item;
                setProfileSelected(item.getView(), true);
                onNetConfigChanged(false); // 应用配置但不显示Toast
                break;
            }
        }
    }

    /* JADX WARN: Type inference failed for: r0v0, types: [com.tencent.qnet.ui.MainActivity$13] */
    private void reportLogin() {
        new Thread() { // from class: com.tencent.qnet.ui.MainActivity.13
            @Override // java.lang.Thread, java.lang.Runnable
            public void run() {
                try {
                    String userName = GlobalSettings.getInstance().getUserName();
                    Date date = new Date();
                    SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
                    SimpleDateFormat simpleDateFormat2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    String format = simpleDateFormat.format(date);
                    String format2 = simpleDateFormat2.format(date);
                    String valueOf = String.valueOf(SystemUtil.getAppVersionInfo(QnetApplication.getContext()).versionCode);
                    boolean z = false;
                    String lastLoginDate = GlobalSettings.getInstance().getLastLoginDate();
                    if (lastLoginDate.isEmpty() || !format.equals(lastLoginDate)) {
                        z = ServerHelper.reportUserInfo(userName, format2, valueOf);
                    }
                    if (z) {
                        GlobalSettings.getInstance().setLastLoginDate(format);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }.start();
    }

    private void setButtonTextStatus(boolean z) {
        this.mIsRunning = z;
        if (z) {
            this.mButtonTest.setText(getResources().getString(R.string.button_stop_test));
        } else {
            this.mButtonTest.setText(getResources().getString(R.string.button_start_test));
        }
    }

    private void setProfileSelected(View view, boolean z) {
        if (view != null) {
            TextView textView = (TextView) view.findViewById(R.id.template_name);
            TextView textView2 = (TextView) view.findViewById(R.id.template_desc);
            View findViewById = view.findViewById(R.id.select_indicator);
            int color = getResources().getColor(R.color.colorPrimary);
            textView.setTextColor(z ? color : getResources().getColor(17170441));
            textView2.setTextColor(z ? color : getResources().getColor(17170432));
            if (!z) {
                color = getResources().getColor(17170445);
            }
            findViewById.setBackgroundColor(color);
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void showCheckVersionDialog(String str) {
        String string = getResources().getString(R.string.versionUpdateTitle);
        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        builder.setTitle(string);
        builder.setMessage(str);
        builder.setPositiveButton(getResources().getString(2131755192), new DialogInterface.OnClickListener() { // from class: com.tencent.qnet.ui.MainActivity.12
            @Override // android.content.DialogInterface.OnClickListener
            public void onClick(DialogInterface dialogInterface, int i) {
                MainActivity.this.finishAndRemoveTask();
            }
        });
        builder.show();
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void showNormalDialog(String str) {
        String string = getResources().getString(R.string.profileDownLoadTitle);
        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        builder.setTitle(string);
        builder.setMessage((str == null || !str.trim().equals(QnetGlobalDefine.QNET_HTTP_RESP_TYPE.HTTP_RESP_TIMEOUT.toString())) ? (str == null || !str.trim().equals(QnetGlobalDefine.QNET_HTTP_RESP_TYPE.HTTP_RESP_UNDEFINED.toString())) ? getResources().getString(R.string.profileDownLoadHttpErrorTips) : getResources().getString(R.string.profileDownLoadHttpErrorTips) : getResources().getString(R.string.profileDownLoadTimeOutTips));
        builder.setPositiveButton(getResources().getString(2131755192), new DialogInterface.OnClickListener() { // from class: com.tencent.qnet.ui.MainActivity.3
            @Override // android.content.DialogInterface.OnClickListener
            public void onClick(DialogInterface dialogInterface, int i) {
                MainActivity.this.finishAndRemoveTask();
            }
        });
        builder.show();
    }

    /* JADX WARN: Type inference failed for: r0v1, types: [com.tencent.qnet.ui.MainActivity$8] */
    private void startVPNService() {
        UpdateNetworkConfig3();
        startService(new Intent(this, LocalVpnService.class));
        new Thread() { // from class: com.tencent.qnet.ui.MainActivity.8
            @Override // java.lang.Thread, java.lang.Runnable
            public void run() {
                try {
                    Thread.sleep(3000L);
                } catch (Exception e) {
                    Log.e(GlobalSettings.LOG_TAG, e.getMessage());
                }
                ServerHelper.reportStartInfo(MainActivity.this.m_currentSelectedProfile.getProfile().Name);
            }
        }.start();
    }

    private void updateTestProgramUI() {
        ImageView imageView = (ImageView) findViewById(R.id.imageViewPkg);
        TextView textView = (TextView) findViewById(R.id.textViewPkg);
        AppInfo proxyApp = AppProxyManager.Instance.getProxyApp();
        if (proxyApp == null) {
            imageView.setImageDrawable(getResources().getDrawable(17301651, null));
            textView.setText(String.format("%s >", getResources().getString(R.string.lable_click_select_progress)));
            return;
        }
        textView.setText(String.format("%s >", proxyApp.getAppLabel()));
        if (proxyApp.getAppIcon() != null) {
            imageView.setImageDrawable(proxyApp.getAppIcon());
            return;
        }
        try {
            imageView.setImageDrawable(getPackageManager().getApplicationIcon(getPackageManager().getApplicationInfo(proxyApp.getPkgName(), 0)));
        } catch (PackageManager.NameNotFoundException e) {
            e.printStackTrace();
        }
    }

    public View getCurrentSelectConfigView() {
        if (this.m_currentSelectedProfile == null) {
            return null;
        }
        return this.m_currentSelectedProfile.getView();
    }

    public NetProfileItem getCurrentSelectedProfile() {
        return this.m_currentSelectedProfile;
    }

    @Override // android.support.v4.app.FragmentActivity, android.app.Activity
    public void onActivityResult(int i, int i2, Intent intent) {
        super.onActivityResult(i, i2, intent);
        if (i == START_VPN_SERVICE_REQUEST_CODE) {
            if (i2 == -1) {
                startVPNService();
            } else {
                setButtonTextStatus(false);
            }
        } else if (i != 2081) {
            if (i == 2343 && i2 == -1) {
                onEditActivityReturn(intent);
            }
        } else if (i2 == -1) {
            intent.getStringExtra("ProxyProgram");
            updateTestProgramUI();
        }
    }

    @Override // android.support.v7.app.AppCompatActivity, android.support.v4.app.FragmentActivity, android.support.v4.app.SupportActivity, android.app.Activity
    protected void onCreate(Bundle bundle) {
        C0026.show();
        super.onCreate(bundle);
        setContentView(R.layout.activity_main);
        initGlobalSettings();
        initLogin();
        initUI();
        initMeteorAnimation();
    }

    @Override // android.app.Activity
    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.menu_main, menu);
        this.m_menu = menu;
        menu.findItem(R.id.menu_login_out).setTitle(String.format("%s(%s)", getResources().getString(R.string.menu_logout), GlobalSettings.getInstance().getUserName()));
        return true;
    }

    @Override // android.support.v7.app.AppCompatActivity, android.support.v4.app.FragmentActivity, android.app.Activity
    protected void onPause() {
        super.onPause();
        // 在应用暂停时保存当前选中的配置状态
        saveCurrentSelectedProfile();
        // 暂停流星动画以节省电量
        if (meteorController != null) {
            meteorController.stopMeteorAnimation();
        }
    }

    @Override // android.support.v7.app.AppCompatActivity, android.support.v4.app.FragmentActivity, android.app.Activity
    protected void onStop() {
        super.onStop();
        // 在应用停止时再次保存配置，确保数据持久化
        saveCurrentSelectedProfile();
    }

    @Override // android.support.v7.app.AppCompatActivity, android.support.v4.app.FragmentActivity, android.app.Activity
    protected void onDestroy() {
        LocalVpnService.removeOnStatusChangedListener(this);
        // 在应用销毁时最后一次保存配置
        saveCurrentSelectedProfile();
        super.onDestroy();
    }

    /* 保存当前选中的配置状态 */
    private void saveCurrentSelectedProfile() {
        try {
            if (this.m_currentSelectedProfile != null) {
                // 确保当前选中的配置状态为true
                this.m_currentSelectedProfile.getProfile().Selected = true;
                // 立即保存到本地文件
                ProposalManager.GetInstance().EditProposal(this.m_currentSelectedProfile.getProfile(), true, false);
                Log.d(GlobalSettings.LOG_TAG, "Saved current selected profile: " + this.m_currentSelectedProfile.getProfile().Name);
            }
        } catch (Exception e) {
            Log.e(GlobalSettings.LOG_TAG, "Failed to save current selected profile: " + e.getMessage());
        }
    }

    @Override // android.app.Activity, android.view.KeyEvent.Callback
    public boolean onKeyUp(int i, KeyEvent keyEvent) {
        return i == 4;
    }

    @Override // com.tencent.qnet.core.LocalVpnService.onStatusChangedListener
    @SuppressLint({"DefaultLocale"})
    public void onLogReceived(String str) {
    }

    @Override // android.app.Activity
    public boolean onOptionsItemSelected(MenuItem menuItem) {
        int itemId = menuItem.getItemId();
        if (itemId == R.id.menu_quit) {
            if (LocalVpnService.IsRunning) {
                LocalVpnService.IsRunning = false;
            }
            finishAndRemoveTask();
            return true;
        } else if (itemId == R.id.menu_settings) {
            startActivity(new Intent(this, SettingsActivity.class));
            return true;
        } else {
            if (itemId == R.id.menu_login_out) {
                if (true == LocalVpnService.IsRunning) {
                    new AlertDialog.Builder(this).setMessage(getResources().getString(R.string.menu_logout_tips)).setPositiveButton(getResources().getString(R.string.dialog_btn_OK), new DialogInterface.OnClickListener() { // from class: com.tencent.qnet.ui.MainActivity.1
                        @Override // android.content.DialogInterface.OnClickListener
                        public void onClick(DialogInterface dialogInterface, int i) {
                        }
                    }).create().show();
                } else {
                    LoginManager.logOut(this);
                    ProposalManager.GetInstance().Clear();
                    Intent intent = new Intent();
                    intent.setClass(this, LoginActivity.class);
                    startActivity(intent);
                    finish();
                }
            }
            return super.onOptionsItemSelected(menuItem);
        }
    }

    /* JADX INFO: Access modifiers changed from: protected */
    @Override // android.support.v4.app.FragmentActivity, android.app.Activity
    public void onResume() {
        super.onResume();
        // 恢复流星动画
        if (meteorController != null && !meteorController.isAnimating()) {
            meteorController.startMeteorAnimation();
        }
    }

    @Override // com.tencent.qnet.core.LocalVpnService.onStatusChangedListener
    public void onStatusChanged(String str, Boolean bool) {
        UpdateVPNStatusChanged(bool.booleanValue());
        onLogReceived(str);
        Toast.makeText(this, str, 0).show();
        if (!bool.booleanValue()) {
            FWService.getInstance().stopSelf();
            return;
        }
        startActivity(getPackageManager().getLaunchIntentForPackage(AppProxyManager.Instance.getProxyApp().getPkgName()));
        startService(new Intent(this, FWService.class));
    }

    public void profileChangedByFW(String str, boolean z) {
        List<NetProfileItem> items = ((NetProfileAdapter) this.m_profileListView.getAdapter()).getItems();
        for (int i = 0; i < items.size(); i++) {
            NetProfileItem netProfileItem = items.get(i);
            if (netProfileItem.getProfile().ID.equals(str)) {
                if (this.m_currentSelectedProfile != null) {
                    this.m_currentSelectedProfile.getProfile().Selected = false;
                    setProfileSelected(this.m_currentSelectedProfile.getView(), false);
                }
                onNetProfileChanged(netProfileItem, z);
                return;
            }
        }
    }

    public void updateProfileSelected(NetProfileItem netProfileItem) {
        if (!netProfileItem.getProfile().Selected) {
            setProfileSelected(netProfileItem.getView(), false);
            return;
        }
        if (this.m_currentSelectedProfile != null) {
            setProfileSelected(this.m_currentSelectedProfile.getView(), false);
        }
        this.m_currentSelectedProfile = netProfileItem;
        setProfileSelected(this.m_currentSelectedProfile.getView(), true);
        if (this.m_currentEditID == null || !this.m_currentEditID.equals(this.m_currentSelectedProfile.getProfile().ID)) {
            return;
        }
        onNetConfigChanged(true);
    }

    /**
     * 初始化流星动画
     */
    private void initMeteorAnimation() {
        try {
            // 创建流星动画控制器
            meteorController = new MeteorAnimationController(this);

            // 延迟启动动画，确保界面完全加载
            new Handler().postDelayed(new Runnable() {
                @Override
                public void run() {
                    startSimpleAnimations();
                    // 同时启动流星动画
                    if (meteorController != null) {
                        meteorController.startMeteorAnimation();
                    }
                }
            }, 2000); // 增加延迟到2秒
        } catch (Exception e) {
            Log.e("MainActivity", "Failed to initialize meteor animation", e);
        }
    }

    /**
     * 启动满屏星空动画效果
     */
    private void startSimpleAnimations() {
        try {
            Log.d("MainActivity", "Starting fullscreen star animations...");

            // 为所有24个星星添加不同的动画效果
            for (int i = 1; i <= 24; i++) {
                String starId = "star_" + i;
                int resId = getResources().getIdentifier(starId, "id", getPackageName());
                ImageView star = findViewById(resId);

                if (star != null) {
                    // 根据星星编号选择不同的动画类型
                    int animType = i % 4;
                    switch (animType) {
                        case 0:
                            startBlinkAnimation(star, starId, 800 + (i * 50)); // 不同的闪烁速度
                            break;
                        case 1:
                            startMoveAnimation(star, starId, 1000 + (i * 100)); // 不同的移动速度
                            break;
                        case 2:
                            startVerticalMoveAnimation(star, starId, 1200 + (i * 80)); // 不同的垂直移动速度
                            break;
                        case 3:
                            startScaleAnimation(star, starId, 1500 + (i * 60)); // 缩放动画
                            break;
                    }

                    Log.d("MainActivity", "Started animation for " + starId + " with type " + animType);
                }
            }

        } catch (Exception e) {
            Log.e("MainActivity", "Failed to start animations", e);
        }
    }

    /**
     * 左右移动动画
     */
    private void startMoveAnimation(final ImageView view, String name, int duration) {
        TranslateAnimation moveAnim = new TranslateAnimation(0, 80, 0, 0);
        moveAnim.setDuration(duration);
        moveAnim.setRepeatCount(Animation.INFINITE);
        moveAnim.setRepeatMode(Animation.REVERSE);
        view.startAnimation(moveAnim);
        Log.d("MainActivity", "Started move animation for " + name + " duration: " + duration);
    }

    /**
     * 闪烁动画
     */
    private void startBlinkAnimation(final ImageView view, String name, int duration) {
        AlphaAnimation blinkAnim = new AlphaAnimation(1.0f, 0.2f);
        blinkAnim.setDuration(duration);
        blinkAnim.setRepeatCount(Animation.INFINITE);
        blinkAnim.setRepeatMode(Animation.REVERSE);
        view.startAnimation(blinkAnim);
        Log.d("MainActivity", "Started blink animation for " + name + " duration: " + duration);
    }

    /**
     * 上下移动动画
     */
    private void startVerticalMoveAnimation(final ImageView view, String name, int duration) {
        TranslateAnimation moveAnim = new TranslateAnimation(0, 0, 0, 40);
        moveAnim.setDuration(duration);
        moveAnim.setRepeatCount(Animation.INFINITE);
        moveAnim.setRepeatMode(Animation.REVERSE);
        view.startAnimation(moveAnim);
        Log.d("MainActivity", "Started vertical move animation for " + name + " duration: " + duration);
    }

    /**
     * 缩放动画
     */
    private void startScaleAnimation(final ImageView view, String name, int duration) {
        ScaleAnimation scaleAnim = new ScaleAnimation(1.0f, 1.5f, 1.0f, 1.5f,
            Animation.RELATIVE_TO_SELF, 0.5f, Animation.RELATIVE_TO_SELF, 0.5f);
        scaleAnim.setDuration(duration);
        scaleAnim.setRepeatCount(Animation.INFINITE);
        scaleAnim.setRepeatMode(Animation.REVERSE);
        view.startAnimation(scaleAnim);
        Log.d("MainActivity", "Started scale animation for " + name + " duration: " + duration);
    }


}
